{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst MessageCircleHeart = createLucideIcon(\"MessageCircleHeart\", [[\"path\", {\n  d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\",\n  key: \"vv11sd\"\n}], [\"path\", {\n  d: \"M15.8 9.2a2.5 2.5 0 0 0-3.5 0l-.3.4-.35-.3a2.42 2.42 0 1 0-3.2 3.6l3.6 3.5 3.6-3.5c1.2-1.2 1.1-2.7.2-3.7\",\n  key: \"43lnbm\"\n}]]);\nexport { MessageCircleHeart as default };", "map": {"version": 3, "names": ["MessageCircleHeart", "createLucideIcon", "d", "key"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\message-circle-heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircleHeart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+CiAgPHBhdGggZD0iTTE1LjggOS4yYTIuNSAyLjUgMCAwIDAtMy41IDBsLS4zLjQtLjM1LS4zYTIuNDIgMi40MiAwIDEgMC0zLjIgMy42bDMuNiAzLjUgMy42LTMuNWMxLjItMS4yIDEuMS0yLjcuMi0zLjciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle-heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircleHeart = createLucideIcon('MessageCircleHeart', [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n  [\n    'path',\n    {\n      d: 'M15.8 9.2a2.5 2.5 0 0 0-3.5 0l-.3.4-.35-.3a2.42 2.42 0 1 0-3.2 3.6l3.6 3.5 3.6-3.5c1.2-1.2 1.1-2.7.2-3.7',\n      key: '43lnbm',\n    },\n  ],\n]);\n\nexport default MessageCircleHeart;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,kBAAA,GAAqBC,gBAAA,CAAiB,oBAAsB,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}