{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst HandPlatter = createLucideIcon(\"HandPlatter\", [[\"path\", {\n  d: \"M12 3V2\",\n  key: \"ar7q03\"\n}], [\"path\", {\n  d: \"M5 10a7.1 7.1 0 0 1 14 0\",\n  key: \"1t9y3n\"\n}], [\"path\", {\n  d: \"M4 10h16\",\n  key: \"img6z1\"\n}], [\"path\", {\n  d: \"M2 14h12a2 2 0 1 1 0 4h-2\",\n  key: \"loyjft\"\n}], [\"path\", {\n  d: \"m15.4 17.4 3.2-2.8a2 2 0 0 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2L5 18\",\n  key: \"1rixiy\"\n}], [\"path\", {\n  d: \"M5 14v7H2\",\n  key: \"3mujks\"\n}]]);\nexport { HandPlatter as default };", "map": {"version": 3, "names": ["HandPlatter", "createLucideIcon", "d", "key"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\hand-platter.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name HandPlatter\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM1YyIiAvPgogIDxwYXRoIGQ9Ik01IDEwYTcuMSA3LjEgMCAwIDEgMTQgMCIgLz4KICA8cGF0aCBkPSJNNCAxMGgxNiIgLz4KICA8cGF0aCBkPSJNMiAxNGgxMmEyIDIgMCAxIDEgMCA0aC0yIiAvPgogIDxwYXRoIGQ9Im0xNS40IDE3LjQgMy4yLTIuOGEyIDIgMCAwIDEgMi44IDIuOWwtMy42IDMuM2MtLjcuOC0xLjcgMS4yLTIuOCAxLjJoLTRjLTEuMSAwLTIuMS0uNC0yLjgtMS4yTDUgMTgiIC8+CiAgPHBhdGggZD0iTTUgMTR2N0gyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/hand-platter\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HandPlatter = createLucideIcon('HandPlatter', [\n  ['path', { d: 'M12 3V2', key: 'ar7q03' }],\n  ['path', { d: 'M5 10a7.1 7.1 0 0 1 14 0', key: '1t9y3n' }],\n  ['path', { d: 'M4 10h16', key: 'img6z1' }],\n  ['path', { d: 'M2 14h12a2 2 0 1 1 0 4h-2', key: 'loyjft' }],\n  [\n    'path',\n    {\n      d: 'm15.4 17.4 3.2-2.8a2 2 0 0 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2L5 18',\n      key: '1rixiy',\n    },\n  ],\n  ['path', { d: 'M5 14v7H2', key: '3mujks' }],\n]);\n\nexport default HandPlatter;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}