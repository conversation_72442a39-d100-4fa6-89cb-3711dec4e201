{"ast": null, "code": "import _objectSpread from\"C:/MY_PROJECTS/youtube-stats-tracker-modernized/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{useChannelStore}from'../store/channelStore';import CreateChannel<PERSON>istButton from'./CreateChannelListButton';import Channel<PERSON>istItem from'./ChannelListItem';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TrackedChannels=()=>{const{channelLists,selectedList,addChannelList,removeChannelList,setSelectedList}=useChannelStore();const handleCreateList=name=>{const newList={id:\"list-\".concat(Date.now()),name,channels:[],created_at:new Date().toISOString(),updated_at:new Date().toISOString()};addChannelList(newList);};const handleRename=(id,newName)=>{// TODO: Implement rename functionality\nconsole.log('Rename list:',id,newName);};const handleDuplicate=id=>{const listToDuplicate=channelLists.find(list=>list.id===id);if(listToDuplicate){const duplicatedList=_objectSpread(_objectSpread({},listToDuplicate),{},{id:\"list-\".concat(Date.now()),name:\"\".concat(listToDuplicate.name,\" (Copy)\"),created_at:new Date().toISOString(),updated_at:new Date().toISOString()});addChannelList(duplicatedList);}};const handleDelete=id=>{removeChannelList(id);if(selectedList===id&&channelLists.length>1){var _remainingLists$;const remainingLists=channelLists.filter(list=>list.id!==id);setSelectedList(((_remainingLists$=remainingLists[0])===null||_remainingLists$===void 0?void 0:_remainingLists$.id)||null);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 bg-dark-950 p-6\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"mb-8\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-white mb-2\",children:\"Tracked channels\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-6 max-w-2xl\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:/*#__PURE__*/_jsx(CreateChannelListButton,{onCreate:handleCreateList})}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:channelLists.map(list=>/*#__PURE__*/_jsx(ChannelListItem,{list:list,isSelected:selectedList===list.id,onClick:()=>setSelectedList(list.id),onRename:handleRename,onDuplicate:handleDuplicate,onDelete:handleDelete},list.id))})]})]});};export default TrackedChannels;", "map": {"version": 3, "names": ["React", "useChannelStore", "CreateChannelListButton", "ChannelListItem", "jsx", "_jsx", "jsxs", "_jsxs", "TrackedChannels", "channelLists", "selectedList", "addChannelList", "removeChannelList", "setSelectedList", "handleCreateList", "name", "newList", "id", "concat", "Date", "now", "channels", "created_at", "toISOString", "updated_at", "handleRename", "newName", "console", "log", "handleDuplicate", "listToDuplicate", "find", "list", "duplicatedList", "_objectSpread", "handleDelete", "length", "_remainingLists$", "remainingLists", "filter", "className", "children", "onCreate", "map", "isSelected", "onClick", "onRename", "onDuplicate", "onDelete"], "sources": ["C:/MY_PROJECTS/youtube-stats-tracker-modernized/frontend/src/components/TrackedChannels.tsx"], "sourcesContent": ["import React from 'react';\nimport { useChannelStore } from '../store/channelStore';\nimport { ChannelList } from '../types';\nimport CreateChannelListButton from './CreateChannelListButton';\nimport ChannelListItem from './ChannelListItem';\n\nconst TrackedChannels: React.FC = () => {\n  const {\n    channelLists,\n    selectedList,\n    addChannelList,\n    removeChannelList,\n    setSelectedList,\n  } = useChannelStore();\n\n  const handleCreateList = (name: string) => {\n    const newList: ChannelList = {\n      id: `list-${Date.now()}`,\n      name,\n      channels: [],\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    };\n    addChannelList(newList);\n  };\n\n  const handleRename = (id: string, newName: string) => {\n    // TODO: Implement rename functionality\n    console.log('Rename list:', id, newName);\n  };\n\n  const handleDuplicate = (id: string) => {\n    const listToDuplicate = channelLists.find(list => list.id === id);\n    if (listToDuplicate) {\n      const duplicatedList: ChannelList = {\n        ...listToDuplicate,\n        id: `list-${Date.now()}`,\n        name: `${listToDuplicate.name} (Copy)`,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      };\n      addChannelList(duplicatedList);\n    }\n  };\n\n  const handleDelete = (id: string) => {\n    removeChannelList(id);\n    if (selectedList === id && channelLists.length > 1) {\n      const remainingLists = channelLists.filter(list => list.id !== id);\n      setSelectedList(remainingLists[0]?.id || null);\n    }\n  };\n\n  return (\n    <div className=\"flex-1 bg-dark-950 p-6\">\n      <header className=\"mb-8\">\n        <h1 className=\"text-2xl font-bold text-white mb-2\">Tracked channels</h1>\n      </header>\n\n      <div className=\"grid grid-cols-2 gap-6 max-w-2xl\">\n        <div className=\"space-y-4\">\n          <CreateChannelListButton onCreate={handleCreateList} />\n        </div>\n        \n        <div className=\"space-y-2\">\n          {channelLists.map((list) => (\n            <ChannelListItem\n              key={list.id}\n              list={list}\n              isSelected={selectedList === list.id}\n              onClick={() => setSelectedList(list.id)}\n              onRename={handleRename}\n              onDuplicate={handleDuplicate}\n              onDelete={handleDelete}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrackedChannels;\n"], "mappings": "6IAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,eAAe,KAAQ,uBAAuB,CAEvD,MAAO,CAAAC,uBAAuB,KAAM,2BAA2B,CAC/D,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CACJC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,iBAAiB,CACjBC,eACF,CAAC,CAAGZ,eAAe,CAAC,CAAC,CAErB,KAAM,CAAAa,gBAAgB,CAAIC,IAAY,EAAK,CACzC,KAAM,CAAAC,OAAoB,CAAG,CAC3BC,EAAE,SAAAC,MAAA,CAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CACxBL,IAAI,CACJM,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACpCC,UAAU,CAAE,GAAI,CAAAL,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CACrC,CAAC,CACDZ,cAAc,CAACK,OAAO,CAAC,CACzB,CAAC,CAED,KAAM,CAAAS,YAAY,CAAGA,CAACR,EAAU,CAAES,OAAe,GAAK,CACpD;AACAC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEX,EAAE,CAAES,OAAO,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAG,eAAe,CAAIZ,EAAU,EAAK,CACtC,KAAM,CAAAa,eAAe,CAAGrB,YAAY,CAACsB,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACf,EAAE,GAAKA,EAAE,CAAC,CACjE,GAAIa,eAAe,CAAE,CACnB,KAAM,CAAAG,cAA2B,CAAAC,aAAA,CAAAA,aAAA,IAC5BJ,eAAe,MAClBb,EAAE,SAAAC,MAAA,CAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CACxBL,IAAI,IAAAG,MAAA,CAAKY,eAAe,CAACf,IAAI,WAAS,CACtCO,UAAU,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACpCC,UAAU,CAAE,GAAI,CAAAL,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,EACrC,CACDZ,cAAc,CAACsB,cAAc,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAIlB,EAAU,EAAK,CACnCL,iBAAiB,CAACK,EAAE,CAAC,CACrB,GAAIP,YAAY,GAAKO,EAAE,EAAIR,YAAY,CAAC2B,MAAM,CAAG,CAAC,CAAE,KAAAC,gBAAA,CAClD,KAAM,CAAAC,cAAc,CAAG7B,YAAY,CAAC8B,MAAM,CAACP,IAAI,EAAIA,IAAI,CAACf,EAAE,GAAKA,EAAE,CAAC,CAClEJ,eAAe,CAAC,EAAAwB,gBAAA,CAAAC,cAAc,CAAC,CAAC,CAAC,UAAAD,gBAAA,iBAAjBA,gBAAA,CAAmBpB,EAAE,GAAI,IAAI,CAAC,CAChD,CACF,CAAC,CAED,mBACEV,KAAA,QAAKiC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpC,IAAA,WAAQmC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACtBpC,IAAA,OAAImC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,CAClE,CAAC,cAETlC,KAAA,QAAKiC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CpC,IAAA,QAAKmC,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBpC,IAAA,CAACH,uBAAuB,EAACwC,QAAQ,CAAE5B,gBAAiB,CAAE,CAAC,CACpD,CAAC,cAENT,IAAA,QAAKmC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBhC,YAAY,CAACkC,GAAG,CAAEX,IAAI,eACrB3B,IAAA,CAACF,eAAe,EAEd6B,IAAI,CAAEA,IAAK,CACXY,UAAU,CAAElC,YAAY,GAAKsB,IAAI,CAACf,EAAG,CACrC4B,OAAO,CAAEA,CAAA,GAAMhC,eAAe,CAACmB,IAAI,CAACf,EAAE,CAAE,CACxC6B,QAAQ,CAAErB,YAAa,CACvBsB,WAAW,CAAElB,eAAgB,CAC7BmB,QAAQ,CAAEb,YAAa,EANlBH,IAAI,CAACf,EAOX,CACF,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAT,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}