{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Percent = createLucideIcon(\"Percent\", [[\"line\", {\n  x1: \"19\",\n  x2: \"5\",\n  y1: \"5\",\n  y2: \"19\",\n  key: \"1x9vlm\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"6.5\",\n  r: \"2.5\",\n  key: \"4mh3h7\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"2.5\",\n  key: \"1mdrzq\"\n}]]);\nexport { Percent as default };", "map": {"version": 3, "names": ["Percent", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "cx", "cy", "r"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\percent.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Percent\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTkiIHgyPSI1IiB5MT0iNSIgeTI9IjE5IiAvPgogIDxjaXJjbGUgY3g9IjYuNSIgY3k9IjYuNSIgcj0iMi41IiAvPgogIDxjaXJjbGUgY3g9IjE3LjUiIGN5PSIxNy41IiByPSIyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/percent\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Percent = createLucideIcon('Percent', [\n  ['line', { x1: '19', x2: '5', y1: '5', y2: '19', key: '1x9vlm' }],\n  ['circle', { cx: '6.5', cy: '6.5', r: '2.5', key: '4mh3h7' }],\n  ['circle', { cx: '17.5', cy: '17.5', r: '2.5', key: '1mdrzq' }],\n]);\n\nexport default Percent;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAU;EAAEC,EAAI;EAAOC,EAAI;EAAOC,CAAG;EAAOH,GAAK;AAAA,CAAU,GAC5D,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,EAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}