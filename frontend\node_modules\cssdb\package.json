{"name": "cssdb", "version": "7.11.2", "type": "module", "description": "A comprehensive list of CSS features and their positions in the process of becoming implemented web standards", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "funding": [{"type": "opencollective", "url": "https://opencollective.com/csstools"}, {"type": "github", "url": "https://github.com/sponsors/csstools"}], "repository": "csstools/cssdb", "homepage": "https://github.com/csstools/cssdb#readme", "bugs": "https://github.com/csstools/cssdb/issues", "main": "cssdb.json", "module": "cssdb.mjs", "files": ["cssdb.json", "cssdb.mjs"], "exports": {".": {"import": "./cssdb.mjs", "require": "./cssdb.json", "default": "./cssdb.json"}}, "scripts": {"start": "node ./tasks/preview-site.mjs", "prestart": "npm run build", "build": "node ./tasks/render-site.mjs", "prebuild": "npm run preparesite", "prepublishOnly": "npm run populatedb", "populatedb": "node tasks/populate-db.mjs", "create-badges": "node tasks/write-stage-badges.mjs && node tasks/write-baseline-badges.mjs", "preparesite": "npm run populatedb && npm run create-badges && npm run buildcss", "buildcss": "postcss src/styles/style.css -d dist/styles -m", "test": "npm run test:css && npm run test:json", "test:css": "stylelint src/styles/style.css", "test:json": "node tasks/test.cjs", "test:doc-links": "node tasks/check-doc-links.mjs"}, "devDependencies": {"@mdn/browser-compat-data": "^5.5.14", "browserslist": "^4.22.3", "glob": "^10.3.7", "lodash.get": "^4.4.2", "postcss": "^8.4.35", "postcss-cli": "^11.0.0", "postcss-preset-env": "^9.4.0", "semver": "^7.6.0", "stylelint": "^16.2.1", "stylelint-config-standard": "^36.0.0"}, "stylelint": {"extends": "stylelint-config-standard", "rules": {"property-no-unknown": [true, {"ignoreProperties": ["font-smoothing"]}], "selector-class-pattern": null, "no-descending-specificity": null, "value-keyword-case": null}}, "keywords": ["css", "features", "specifications", "stages"], "volta": {"node": "20.10.0"}}