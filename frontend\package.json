{"name": "velio-youtube-tracker", "version": "2.0.0", "private": true, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "axios": "^1.7.7", "chart.js": "^4.4.6", "clsx": "^2.1.1", "framer-motion": "^12.19.2", "lucide-react": "^0.454.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "web-vitals": "^4.2.3", "zustand": "^5.0.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://backend:8000", "devDependencies": {"eslint-config-prettier": "^10.1.5"}}