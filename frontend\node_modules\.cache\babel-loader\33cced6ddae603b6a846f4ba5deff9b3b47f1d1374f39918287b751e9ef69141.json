{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst LampDesk = createLucideIcon(\"LampDesk\", [[\"path\", {\n  d: \"m14 5-3 3 2 7 8-8-7-2Z\",\n  key: \"1b0msb\"\n}], [\"path\", {\n  d: \"m14 5-3 3-3-3 3-3 3 3Z\",\n  key: \"1uemms\"\n}], [\"path\", {\n  d: \"M9.5 6.5 4 12l3 6\",\n  key: \"1bx08v\"\n}], [\"path\", {\n  d: \"M3 22v-2c0-1.1.9-2 2-2h4a2 2 0 0 1 2 2v2H3Z\",\n  key: \"wap775\"\n}]]);\nexport { LampDesk as default };", "map": {"version": 3, "names": ["LampDesk", "createLucideIcon", "d", "key"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\lamp-desk.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LampDesk\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTQgNS0zIDMgMiA3IDgtOC03LTJaIiAvPgogIDxwYXRoIGQ9Im0xNCA1LTMgMy0zLTMgMy0zIDMgM1oiIC8+CiAgPHBhdGggZD0iTTkuNSA2LjUgNCAxMmwzIDYiIC8+CiAgPHBhdGggZD0iTTMgMjJ2LTJjMC0xLjEuOS0yIDItMmg0YTIgMiAwIDAgMSAyIDJ2MkgzWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/lamp-desk\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampDesk = createLucideIcon('LampDesk', [\n  ['path', { d: 'm14 5-3 3 2 7 8-8-7-2Z', key: '1b0msb' }],\n  ['path', { d: 'm14 5-3 3-3-3 3-3 3 3Z', key: '1uemms' }],\n  ['path', { d: 'M9.5 6.5 4 12l3 6', key: '1bx08v' }],\n  ['path', { d: 'M3 22v-2c0-1.1.9-2 2-2h4a2 2 0 0 1 2 2v2H3Z', key: 'wap775' }],\n]);\n\nexport default LampDesk;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}