{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SignpostBig = createLucideIcon(\"SignpostBig\", [[\"path\", {\n  d: \"M10 9H4L2 7l2-2h6\",\n  key: \"1hq7x2\"\n}], [\"path\", {\n  d: \"M14 5h6l2 2-2 2h-6\",\n  key: \"bv62ej\"\n}], [\"path\", {\n  d: \"M10 22V4a2 2 0 1 1 4 0v18\",\n  key: \"eqpcf2\"\n}], [\"path\", {\n  d: \"M8 22h8\",\n  key: \"rmew8v\"\n}]]);\nexport { SignpostBig as default };", "map": {"version": 3, "names": ["SignpostBig", "createLucideIcon", "d", "key"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\signpost-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SignpostBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgOUg0TDIgN2wyLTJoNiIgLz4KICA8cGF0aCBkPSJNMTQgNWg2bDIgMi0yIDJoLTYiIC8+CiAgPHBhdGggZD0iTTEwIDIyVjRhMiAyIDAgMSAxIDQgMHYxOCIgLz4KICA8cGF0aCBkPSJNOCAyMmg4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/signpost-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SignpostBig = createLucideIcon('SignpostBig', [\n  ['path', { d: 'M10 9H4L2 7l2-2h6', key: '1hq7x2' }],\n  ['path', { d: 'M14 5h6l2 2-2 2h-6', key: 'bv62ej' }],\n  ['path', { d: 'M10 22V4a2 2 0 1 1 4 0v18', key: 'eqpcf2' }],\n  ['path', { d: 'M8 22h8', key: 'rmew8v' }],\n]);\n\nexport default SignpostBig;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}