import React from 'react';
import { useChannelStore } from '../store/channelStore';
import { ChannelList } from '../types';
import CreateChannelListButton from './CreateChannelListButton';
import ChannelListItem from './ChannelListItem';

const TrackedChannels: React.FC = () => {
  const {
    channelLists,
    selectedList,
    addChannelList,
    removeChannelList,
    setSelectedList,
  } = useChannelStore();

  const handleCreateList = (name: string) => {
    const newList: ChannelList = {
      id: `list-${Date.now()}`,
      name,
      channels: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    addChannelList(newList);
  };

  const handleRename = (id: string, newName: string) => {
    // TODO: Implement rename functionality
    console.log('Rename list:', id, newName);
  };

  const handleDuplicate = (id: string) => {
    const listToDuplicate = channelLists.find(list => list.id === id);
    if (listToDuplicate) {
      const duplicatedList: ChannelList = {
        ...listToDuplicate,
        id: `list-${Date.now()}`,
        name: `${listToDuplicate.name} (Copy)`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      addChannelList(duplicatedList);
    }
  };

  const handleDelete = (id: string) => {
    removeChannelList(id);
    if (selectedList === id && channelLists.length > 1) {
      const remainingLists = channelLists.filter(list => list.id !== id);
      setSelectedList(remainingLists[0]?.id || null);
    }
  };

  return (
    <div className="flex-1 bg-dark-950 p-6">
      <header className="mb-8">
        <h1 className="text-2xl font-bold text-white mb-2">Tracked channels</h1>
      </header>

      <div className="grid grid-cols-2 gap-6 max-w-2xl">
        <div className="space-y-4">
          <CreateChannelListButton onCreate={handleCreateList} />
        </div>
        
        <div className="space-y-2">
          {channelLists.map((list) => (
            <ChannelListItem
              key={list.id}
              list={list}
              isSelected={selectedList === list.id}
              onClick={() => setSelectedList(list.id)}
              onRename={handleRename}
              onDuplicate={handleDuplicate}
              onDelete={handleDelete}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TrackedChannels;
