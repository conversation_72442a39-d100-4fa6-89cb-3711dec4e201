/*! For license information please see main.4b1535d0.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var r,l={},s=null,c=null;for(r in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!u.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:s,ref:c,props:l,_owner:i.current}}t.jsx=s,t.jsxs=s},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),u=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function b(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var k=y.prototype=new b;k.constructor=y,h(k,v.prototype),k.isPureReactComponent=!0;var w=Array.isArray,x=Object.prototype.hasOwnProperty,S={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)x.call(t,a)&&!E.hasOwnProperty(a)&&(l[a]=t[a]);var u=arguments.length-2;if(1===u)l.children=r;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];l.children=s}if(e&&e.defaultProps)for(a in u=e.defaultProps)void 0===l[a]&&(l[a]=u[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:S.current}}function _(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function z(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var u=!1;if(null===e)u=!0;else switch(i){case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0}}if(u)return o=o(u=e),e=""===l?"."+P(u,0):l,w(o)?(a="",null!=e&&(a=e.replace(N,"$&/")+"/"),z(o,t,a,"",function(e){return e})):null!=o&&(_(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||u&&u.key===o.key?"":(""+o.key).replace(N,"$&/")+"/")+e)),t.push(o)),1;if(u=0,l=""===l?".":l+":",w(e))for(var s=0;s<e.length;s++){var c=l+P(i=e[s],s);u+=z(i,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),s=0;!(i=e.next()).done;)u+=z(i=i.value,t,a,c=l+P(i,s++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return u}function L(e,t,n){if(null==e)return e;var r=[],a=0;return z(e,r,"","",function(e){return t.call(n,e,a++)}),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O={current:null},R={transition:null},M={ReactCurrentDispatcher:O,ReactCurrentBatchConfig:R,ReactCurrentOwner:S};function j(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:L,forEach:function(e,t,n){L(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return L(e,function(){t++}),t},toArray:function(e){return L(e,function(e){return e})||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=y,t.StrictMode=l,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M,t.act=j,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=S.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(s in t)x.call(t,s)&&!E.hasOwnProperty(s)&&(a[s]=void 0===t[s]&&void 0!==u?u[s]:t[s])}var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){u=Array(s);for(var c=0;c<s;c++)u[c]=arguments[c+2];a.children=u}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=R.transition;R.transition={};try{e()}finally{R.transition=t}},t.unstable_act=j,t.useCallback=function(e,t){return O.current.useCallback(e,t)},t.useContext=function(e){return O.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return O.current.useDeferredValue(e)},t.useEffect=function(e,t){return O.current.useEffect(e,t)},t.useId=function(){return O.current.useId()},t.useImperativeHandle=function(e,t,n){return O.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return O.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return O.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return O.current.useMemo(e,t)},t.useReducer=function(e,t,n){return O.current.useReducer(e,t,n)},t.useRef=function(e){return O.current.useRef(e)},t.useState=function(e){return O.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return O.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return O.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,u=e[i],s=i+1,c=e[s];if(0>l(u,n))s<a&&0>l(c,u)?(e[r]=c,e[s]=n,r=s):(e[r]=u,e[i]=n,r=i);else{if(!(s<a&&0>l(c,n)))break e;e[r]=c,e[s]=n,r=s}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,u=i.now();t.unstable_now=function(){return i.now()-u}}var s=[],c=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,y="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(s,t)}t=r(c)}}function w(e){if(g=!1,k(e),!h)if(null!==r(s))h=!0,R(x);else{var t=r(c);null!==t&&M(w,t.startTime-e)}}function x(e,n){h=!1,g&&(g=!1,b(_),_=-1),m=!0;var l=p;try{for(k(n),f=r(s);null!==f&&(!(f.expirationTime>n)||e&&!z());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(s)&&a(s),k(n)}else a(s);f=r(s)}if(null!==f)var u=!0;else{var d=r(c);null!==d&&M(w,d.startTime-n),u=!1}return u}finally{f=null,p=l,m=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,C=null,_=-1,N=5,P=-1;function z(){return!(t.unstable_now()-P<N)}function L(){if(null!==C){var e=t.unstable_now();P=e;var n=!0;try{n=C(!0,e)}finally{n?S():(E=!1,C=null)}}else E=!1}if("function"===typeof y)S=function(){y(L)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,O=T.port2;T.port1.onmessage=L,S=function(){O.postMessage(null)}}else S=function(){v(L,0)};function R(e){C=e,E||(E=!0,S())}function M(e,n){_=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,R(x))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(s)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(s)&&e===r(c)&&(g?(b(_),_=-1):g=!0,M(w,l-o))):(e.sortIndex=i,n(s,e),h||m||(h=!0,R(x))),e},t.unstable_shouldYield=z,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function u(e,t){s(e,t),s(e+"Capture",t)}function s(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new h(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new h(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new h(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new h(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new h(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function b(e){return e[1].toUpperCase()}function y(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)});var k=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),x=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),N=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),L=Symbol.for("react.suspense_list"),T=Symbol.for("react.memo"),O=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var R=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var M=Symbol.iterator;function j(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=M&&e[M]||e["@@iterator"])?e:null}var D,F=Object.assign;function I(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var U=!1;function $(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var r=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){r=s}e.call(t.prototype)}else{try{throw Error()}catch(s){r=s}e()}}catch(s){if(s&&r&&"string"===typeof s.stack){for(var a=s.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var u="\n"+a[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=o&&0<=i);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?I(e):""}function A(e){switch(e.tag){case 5:return I(e.type);case 16:return I("Lazy");case 13:return I("Suspense");case 19:return I("SuspenseList");case 0:case 2:case 15:return e=$(e.type,!1);case 11:return e=$(e.type.render,!1);case 1:return e=$(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case x:return"Portal";case C:return"Profiler";case E:return"StrictMode";case z:return"Suspense";case L:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case T:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case O:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&y(e,"checked",t,!1)}function Z(e,t){X(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function le(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ue(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var se,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((se=se||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=se.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ve=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ke=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var xe=null,Se=null,Ee=null;function Ce(e){if(e=ka(e)){if("function"!==typeof xe)throw Error(l(280));var t=e.stateNode;t&&(t=xa(t),xe(e.stateNode,e.type,t))}}function _e(e){Se?Ee?Ee.push(e):Ee=[e]:Se=e}function Ne(){if(Se){var e=Se,t=Ee;if(Ee=Se=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Pe(e,t){return e(t)}function Le(){}var Te=!1;function Oe(e,t,n){if(Te)return e(t,n);Te=!0;try{return Pe(e,t,n)}finally{Te=!1,(null!==Se||null!==Ee)&&(Le(),Ne())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=xa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Me=!1;if(c)try{var je={};Object.defineProperty(je,"passive",{get:function(){Me=!0}}),window.addEventListener("test",je,je),window.removeEventListener("test",je,je)}catch(ce){Me=!1}function De(e,t,n,r,a,l,o,i,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(c){this.onError(c)}}var Fe=!1,Ie=null,Ue=!1,$e=null,Ae={onError:function(e){Fe=!0,Ie=e}};function Be(e,t,n,r,a,l,o,i,u){Fe=!1,Ie=null,De.apply(Ae,arguments)}function Ve(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(Ve(e)!==e)throw Error(l(188))}function Qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return He(a),e;if(o===r)return He(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,u=a.child;u;){if(u===n){i=!0,n=a,r=o;break}if(u===r){i=!0,r=a,n=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===n){i=!0,n=o,r=a;break}if(u===r){i=!0,r=o,n=a;break}u=u.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Ke=a.unstable_scheduleCallback,Ge=a.unstable_cancelCallback,Ye=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ze=a.unstable_now,Je=a.unstable_getCurrentPriorityLevel,et=a.unstable_ImmediatePriority,tt=a.unstable_UserBlockingPriority,nt=a.unstable_NormalPriority,rt=a.unstable_LowPriority,at=a.unstable_IdlePriority,lt=null,ot=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(ut(e)/st|0)|0},ut=Math.log,st=Math.LN2;var ct=64,dt=4194304;function ft(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=ft(i):0!==(l&=o)&&(r=ft(l))}else 0!==(o=n&~a)?r=ft(o):0!==l&&(r=ft(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-it(t)),r|=e[n],t&=~a;return r}function mt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function gt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function bt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var kt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,St,Et,Ct,_t,Nt=!1,Pt=[],zt=null,Lt=null,Tt=null,Ot=new Map,Rt=new Map,Mt=[],jt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":zt=null;break;case"dragenter":case"dragleave":Lt=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Ft(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=ka(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function It(e){var t=ya(e.target);if(null!==t){var n=Ve(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void _t(e.priority,function(){Et(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ut(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ka(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);ke=r,n.target.dispatchEvent(r),ke=null,t.shift()}return!0}function $t(e,t,n){Ut(e)&&n.delete(t)}function At(){Nt=!1,null!==zt&&Ut(zt)&&(zt=null),null!==Lt&&Ut(Lt)&&(Lt=null),null!==Tt&&Ut(Tt)&&(Tt=null),Ot.forEach($t),Rt.forEach($t)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Nt||(Nt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,At)))}function Vt(e){function t(t){return Bt(t,e)}if(0<Pt.length){Bt(Pt[0],e);for(var n=1;n<Pt.length;n++){var r=Pt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==zt&&Bt(zt,e),null!==Lt&&Bt(Lt,e),null!==Tt&&Bt(Tt,e),Ot.forEach(t),Rt.forEach(t),n=0;n<Mt.length;n++)(r=Mt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Mt.length&&null===(n=Mt[0]).blockedOn;)It(n),null===n.blockedOn&&Mt.shift()}var Wt=k.ReactCurrentBatchConfig,Ht=!0;function Qt(e,t,n,r){var a=kt,l=Wt.transition;Wt.transition=null;try{kt=1,Kt(e,t,n,r)}finally{kt=a,Wt.transition=l}}function qt(e,t,n,r){var a=kt,l=Wt.transition;Wt.transition=null;try{kt=4,Kt(e,t,n,r)}finally{kt=a,Wt.transition=l}}function Kt(e,t,n,r){if(Ht){var a=Yt(e,t,n,r);if(null===a)Hr(e,t,r,Gt,n),Dt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return zt=Ft(zt,e,t,n,r,a),!0;case"dragenter":return Lt=Ft(Lt,e,t,n,r,a),!0;case"mouseover":return Tt=Ft(Tt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Ot.set(l,Ft(Ot.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Rt.set(l,Ft(Rt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<jt.indexOf(e)){for(;null!==a;){var l=ka(a);if(null!==l&&xt(l),null===(l=Yt(e,t,n,r))&&Hr(e,t,r,Gt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Gt=null;function Yt(e,t,n,r){if(Gt=null,null!==(e=ya(e=we(r))))if(null===(t=Ve(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case et:return 1;case tt:return 4;case nt:case rt:return 16;case at:return 536870912;default:return 16}default:return 16}}var Zt=null,Jt=null,en=null;function tn(){if(en)return en;var e,t,n=Jt,r=n.length,a="value"in Zt?Zt.value:Zt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return en=a.slice(e,1<t?1-t:void 0)}function nn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function rn(){return!0}function an(){return!1}function ln(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?rn:an,this.isPropagationStopped=an,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=rn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=rn)},persist:function(){},isPersistent:rn}),t}var on,un,sn,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dn=ln(cn),fn=F({},cn,{view:0,detail:0}),pn=ln(fn),mn=F({},fn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_n,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(on=e.screenX-sn.screenX,un=e.screenY-sn.screenY):un=on=0,sn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:un}}),hn=ln(mn),gn=ln(F({},mn,{dataTransfer:0})),vn=ln(F({},fn,{relatedTarget:0})),bn=ln(F({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=F({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),kn=ln(yn),wn=ln(F({},cn,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},En={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=En[e])&&!!t[e]}function _n(){return Cn}var Nn=F({},fn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=nn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_n,charCode:function(e){return"keypress"===e.type?nn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?nn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Pn=ln(Nn),zn=ln(F({},mn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Ln=ln(F({},fn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_n})),Tn=ln(F({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=F({},mn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=ln(On),Mn=[9,13,27,32],jn=c&&"CompositionEvent"in window,Dn=null;c&&"documentMode"in document&&(Dn=document.documentMode);var Fn=c&&"TextEvent"in window&&!Dn,In=c&&(!jn||Dn&&8<Dn&&11>=Dn),Un=String.fromCharCode(32),$n=!1;function An(e,t){switch(e){case"keyup":return-1!==Mn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Vn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Qn(e,t,n,r){_e(r),0<(t=qr(t,"onChange")).length&&(n=new dn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Kn=null;function Gn(e){Ur(e,0)}function Yn(e){if(q(wa(e)))return e}function Xn(e,t){if("change"===e)return t}var Zn=!1;if(c){var Jn;if(c){var er="oninput"in document;if(!er){var tr=document.createElement("div");tr.setAttribute("oninput","return;"),er="function"===typeof tr.oninput}Jn=er}else Jn=!1;Zn=Jn&&(!document.documentMode||9<document.documentMode)}function nr(){qn&&(qn.detachEvent("onpropertychange",rr),Kn=qn=null)}function rr(e){if("value"===e.propertyName&&Yn(Kn)){var t=[];Qn(t,Kn,e,we(e)),Oe(Gn,t)}}function ar(e,t,n){"focusin"===e?(nr(),Kn=n,(qn=t).attachEvent("onpropertychange",rr)):"focusout"===e&&nr()}function lr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(Kn)}function or(e,t){if("click"===e)return Yn(t)}function ir(e,t){if("input"===e||"change"===e)return Yn(t)}var ur="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(ur(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ur(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dr(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function fr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function pr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function mr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=pr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fr(n.ownerDocument.documentElement,n)){if(null!==r&&mr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=dr(n,l);var o=dr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,br=null,yr=null,kr=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;kr||null==vr||vr!==K(r)||("selectionStart"in(r=vr)&&mr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=qr(br,"onSelect")).length&&(t=new dn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Er={},Cr={};function _r(e){if(Er[e])return Er[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Cr)return Er[e]=n[t];return e}c&&(Cr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Nr=_r("animationend"),Pr=_r("animationiteration"),zr=_r("animationstart"),Lr=_r("transitionend"),Tr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Tr.set(e,t),u(t,[e])}for(var Mr=0;Mr<Or.length;Mr++){var jr=Or[Mr];Rr(jr.toLowerCase(),"on"+(jr[0].toUpperCase()+jr.slice(1)))}Rr(Nr,"onAnimationEnd"),Rr(Pr,"onAnimationIteration"),Rr(zr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Lr,"onTransitionEnd"),s("onMouseEnter",["mouseout","mouseover"]),s("onMouseLeave",["mouseout","mouseover"]),s("onPointerEnter",["pointerout","pointerover"]),s("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Ir(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,u,s){if(Be.apply(this,arguments),Fe){if(!Fe)throw Error(l(198));var c=Ie;Fe=!1,Ie=null,Ue||(Ue=!0,$e=c)}}(r,t,void 0,e),e.currentTarget=null}function Ur(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],u=i.instance,s=i.currentTarget;if(i=i.listener,u!==l&&a.isPropagationStopped())break e;Ir(a,i,s),l=u}else for(o=0;o<r.length;o++){if(u=(i=r[o]).instance,s=i.currentTarget,i=i.listener,u!==l&&a.isPropagationStopped())break e;Ir(a,i,s),l=u}}}if(Ue)throw e=$e,Ue=!1,$e=null,e}function $r(e,t){var n=t[ga];void 0===n&&(n=t[ga]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ar(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[Br]){e[Br]=!0,o.forEach(function(t){"selectionchange"!==t&&(Fr.has(t)||Ar(t,!1,e),Ar(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Ar("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Xt(t)){case 1:var a=Qt;break;case 4:a=qt;break;default:a=Kt}n=a.bind(null,t,n,e),a=void 0,!Me||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var u=o.tag;if((3===u||4===u)&&((u=o.stateNode.containerInfo)===a||8===u.nodeType&&u.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=ya(i)))return;if(5===(u=o.tag)||6===u){r=l=o;continue e}i=i.parentNode}}r=r.return}Oe(function(){var r=l,a=we(n),o=[];e:{var i=Tr.get(e);if(void 0!==i){var u=dn,s=e;switch(e){case"keypress":if(0===nn(n))break e;case"keydown":case"keyup":u=Pn;break;case"focusin":s="focus",u=vn;break;case"focusout":s="blur",u=vn;break;case"beforeblur":case"afterblur":u=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=gn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=Ln;break;case Nr:case Pr:case zr:u=bn;break;case Lr:u=Tn;break;case"scroll":u=pn;break;case"wheel":u=Rn;break;case"copy":case"cut":case"paste":u=kn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=zn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=Re(m,f))&&c.push(Qr(m,h,p)))),d)break;m=m.return}0<c.length&&(i=new u(i,s,null,n,a),o.push({event:i,listeners:c}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===ke||!(s=n.relatedTarget||n.fromElement)||!ya(s)&&!s[ha])&&(u||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,u?(u=r,null!==(s=(s=n.relatedTarget||n.toElement)?ya(s):null)&&(s!==(d=Ve(s))||5!==s.tag&&6!==s.tag)&&(s=null)):(u=null,s=r),u!==s)){if(c=hn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(c=zn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==u?i:wa(u),p=null==s?i:wa(s),(i=new c(h,m+"leave",u,n,a)).target=d,i.relatedTarget=p,h=null,ya(a)===r&&((c=new c(f,m+"enter",s,n,a)).target=p,c.relatedTarget=d,h=c),d=h,u&&s)e:{for(f=s,m=0,p=c=u;p;p=Kr(p))m++;for(p=0,h=f;h;h=Kr(h))p++;for(;0<m-p;)c=Kr(c),m--;for(;0<p-m;)f=Kr(f),p--;for(;m--;){if(c===f||null!==f&&c===f.alternate)break e;c=Kr(c),f=Kr(f)}c=null}else c=null;null!==u&&Gr(o,i,u,c,!1),null!==s&&null!==d&&Gr(o,d,s,c,!0)}if("select"===(u=(i=r?wa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===u&&"file"===i.type)var g=Xn;else if(Hn(i))if(Zn)g=ir;else{g=lr;var v=ar}else(u=i.nodeName)&&"input"===u.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=or);switch(g&&(g=g(e,r))?Qn(o,g,n,a):(v&&v(e,i,r),"focusout"===e&&(v=i._wrapperState)&&v.controlled&&"number"===i.type&&ee(i,"number",i.value)),v=r?wa(r):window,e){case"focusin":(Hn(v)||"true"===v.contentEditable)&&(vr=v,br=r,yr=null);break;case"focusout":yr=br=vr=null;break;case"mousedown":kr=!0;break;case"contextmenu":case"mouseup":case"dragend":kr=!1,wr(o,n,a);break;case"selectionchange":if(gr)break;case"keydown":case"keyup":wr(o,n,a)}var b;if(jn)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else Vn?An(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(In&&"ko"!==n.locale&&(Vn||"onCompositionStart"!==y?"onCompositionEnd"===y&&Vn&&(b=tn()):(Jt="value"in(Zt=a)?Zt.value:Zt.textContent,Vn=!0)),0<(v=qr(r,y)).length&&(y=new wn(y,e,null,n,a),o.push({event:y,listeners:v}),b?y.data=b:null!==(b=Bn(n))&&(y.data=b))),(b=Fn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:($n=!0,Un);case"textInput":return(e=t.data)===Un&&$n?null:e;default:return null}}(e,n):function(e,t){if(Vn)return"compositionend"===e||!jn&&An(e,t)?(e=tn(),en=Jt=Zt=null,Vn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return In&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=b))}Ur(o,t)})}function Qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Re(e,n))&&r.unshift(Qr(e,l,a)),null!=(l=Re(e,t))&&r.push(Qr(e,l,a))),e=e.return}return r}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Gr(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,u=i.alternate,s=i.stateNode;if(null!==u&&u===r)break;5===i.tag&&null!==s&&(i=s,a?null!=(u=Re(n,l))&&o.unshift(Qr(n,u,i)):a||null!=(u=Re(n,l))&&o.push(Qr(n,u,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Yr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Zr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Xr,"")}function Jr(e,t,n){if(t=Zr(t),Zr(e)!==t&&n)throw Error(l(425))}function ea(){}var ta=null,na=null;function ra(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var aa="function"===typeof setTimeout?setTimeout:void 0,la="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,ia="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(ua)}:aa;function ua(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Vt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Vt(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function da(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fa=Math.random().toString(36).slice(2),pa="__reactFiber$"+fa,ma="__reactProps$"+fa,ha="__reactContainer$"+fa,ga="__reactEvents$"+fa,va="__reactListeners$"+fa,ba="__reactHandles$"+fa;function ya(e){var t=e[pa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[pa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=da(e);null!==e;){if(n=e[pa])return n;e=da(e)}return t}n=(e=n).parentNode}return null}function ka(e){return!(e=e[pa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function xa(e){return e[ma]||null}var Sa=[],Ea=-1;function Ca(e){return{current:e}}function _a(e){0>Ea||(e.current=Sa[Ea],Sa[Ea]=null,Ea--)}function Na(e,t){Ea++,Sa[Ea]=e.current,e.current=t}var Pa={},za=Ca(Pa),La=Ca(!1),Ta=Pa;function Oa(e,t){var n=e.type.contextTypes;if(!n)return Pa;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ra(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ma(){_a(La),_a(za)}function ja(e,t,n){if(za.current!==Pa)throw Error(l(168));Na(za,t),Na(La,n)}function Da(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,V(e)||"Unknown",a));return F({},n,r)}function Fa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Pa,Ta=za.current,Na(za,e),Na(La,La.current),!0}function Ia(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Da(e,t,Ta),r.__reactInternalMemoizedMergedChildContext=e,_a(La),_a(za),Na(za,e)):_a(La),Na(La,n)}var Ua=null,$a=!1,Aa=!1;function Ba(e){null===Ua?Ua=[e]:Ua.push(e)}function Va(){if(!Aa&&null!==Ua){Aa=!0;var e=0,t=kt;try{var n=Ua;for(kt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ua=null,$a=!1}catch(a){throw null!==Ua&&(Ua=Ua.slice(e+1)),Ke(et,Va),a}finally{kt=t,Aa=!1}}return null}var Wa=[],Ha=0,Qa=null,qa=0,Ka=[],Ga=0,Ya=null,Xa=1,Za="";function Ja(e,t){Wa[Ha++]=qa,Wa[Ha++]=Qa,Qa=e,qa=t}function el(e,t,n){Ka[Ga++]=Xa,Ka[Ga++]=Za,Ka[Ga++]=Ya,Ya=e;var r=Xa;e=Za;var a=32-it(r)-1;r&=~(1<<a),n+=1;var l=32-it(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xa=1<<32-it(t)+a|n<<a|r,Za=l+e}else Xa=1<<l|n<<a|r,Za=e}function tl(e){null!==e.return&&(Ja(e,1),el(e,1,0))}function nl(e){for(;e===Qa;)Qa=Wa[--Ha],Wa[Ha]=null,qa=Wa[--Ha],Wa[Ha]=null;for(;e===Ya;)Ya=Ka[--Ga],Ka[Ga]=null,Za=Ka[--Ga],Ka[Ga]=null,Xa=Ka[--Ga],Ka[Ga]=null}var rl=null,al=null,ll=!1,ol=null;function il(e,t){var n=Os(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ul(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,rl=e,al=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,rl=e,al=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ya?{id:Xa,overflow:Za}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Os(18,null,null,0)).stateNode=t,n.return=e,e.child=n,rl=e,al=null,!0);default:return!1}}function sl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function cl(e){if(ll){var t=al;if(t){var n=t;if(!ul(e,t)){if(sl(e))throw Error(l(418));t=ca(n.nextSibling);var r=rl;t&&ul(e,t)?il(r,n):(e.flags=-4097&e.flags|2,ll=!1,rl=e)}}else{if(sl(e))throw Error(l(418));e.flags=-4097&e.flags|2,ll=!1,rl=e}}}function dl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;rl=e}function fl(e){if(e!==rl)return!1;if(!ll)return dl(e),ll=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ra(e.type,e.memoizedProps)),t&&(t=al)){if(sl(e))throw pl(),Error(l(418));for(;t;)il(e,t),t=ca(t.nextSibling)}if(dl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){al=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}al=null}}else al=rl?ca(e.stateNode.nextSibling):null;return!0}function pl(){for(var e=al;e;)e=ca(e.nextSibling)}function ml(){al=rl=null,ll=!1}function hl(e){null===ol?ol=[e]:ol.push(e)}var gl=k.ReactCurrentBatchConfig;function vl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function bl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yl(e){return(0,e._init)(e._payload)}function kl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Ms(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function u(e,t,n,r){return null===t||6!==t.tag?((t=Is(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function s(e,t,n,r){var l=n.type;return l===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===O&&yl(l)===t.type)?((r=a(t,n.props)).ref=vl(e,t,n),r.return=e,r):((r=js(n.type,n.key,n.props,null,e.mode,r)).ref=vl(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Us(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Ds(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Is(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=js(t.type,t.key,t.props,null,e.mode,n)).ref=vl(e,null,t),n.return=e,n;case x:return(t=Us(t,e.mode,n)).return=e,t;case O:return f(e,(0,t._init)(t._payload),n)}if(te(t)||j(t))return(t=Ds(t,e.mode,n,null)).return=e,t;bl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:u(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?s(e,t,n,r):null;case x:return n.key===a?c(e,t,n,r):null;case O:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||j(n))return null!==a?null:d(e,t,n,r,null);bl(e,n)}return null}function m(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return u(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return s(t,e=e.get(null===r.key?n:r.key)||null,r,a);case x:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case O:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||j(r))return d(t,e=e.get(n)||null,r,a,null);bl(t,r)}return null}function h(a,l,i,u){for(var s=null,c=null,d=l,h=l=0,g=null;null!==d&&h<i.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=p(a,d,i[h],u);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),l=o(v,l,h),null===c?s=v:c.sibling=v,c=v,d=g}if(h===i.length)return n(a,d),ll&&Ja(a,h),s;if(null===d){for(;h<i.length;h++)null!==(d=f(a,i[h],u))&&(l=o(d,l,h),null===c?s=d:c.sibling=d,c=d);return ll&&Ja(a,h),s}for(d=r(a,d);h<i.length;h++)null!==(g=m(d,a,h,i[h],u))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),l=o(g,l,h),null===c?s=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),ll&&Ja(a,h),s}function g(a,i,u,s){var c=j(u);if("function"!==typeof c)throw Error(l(150));if(null==(u=c.call(u)))throw Error(l(151));for(var d=c=null,h=i,g=i=0,v=null,b=u.next();null!==h&&!b.done;g++,b=u.next()){h.index>g?(v=h,h=null):v=h.sibling;var y=p(a,h,b.value,s);if(null===y){null===h&&(h=v);break}e&&h&&null===y.alternate&&t(a,h),i=o(y,i,g),null===d?c=y:d.sibling=y,d=y,h=v}if(b.done)return n(a,h),ll&&Ja(a,g),c;if(null===h){for(;!b.done;g++,b=u.next())null!==(b=f(a,b.value,s))&&(i=o(b,i,g),null===d?c=b:d.sibling=b,d=b);return ll&&Ja(a,g),c}for(h=r(a,h);!b.done;g++,b=u.next())null!==(b=m(h,a,g,b.value,s))&&(e&&null!==b.alternate&&h.delete(null===b.key?g:b.key),i=o(b,i,g),null===d?c=b:d.sibling=b,d=b);return e&&h.forEach(function(e){return t(a,e)}),ll&&Ja(a,g),c}return function e(r,l,o,u){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var s=o.key,c=l;null!==c;){if(c.key===s){if((s=o.type)===S){if(7===c.tag){n(r,c.sibling),(l=a(c,o.props.children)).return=r,r=l;break e}}else if(c.elementType===s||"object"===typeof s&&null!==s&&s.$$typeof===O&&yl(s)===c.type){n(r,c.sibling),(l=a(c,o.props)).ref=vl(r,c,o),l.return=r,r=l;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===S?((l=Ds(o.props.children,r.mode,u,o.key)).return=r,r=l):((u=js(o.type,o.key,o.props,null,r.mode,u)).ref=vl(r,l,o),u.return=r,r=u)}return i(r);case x:e:{for(c=o.key;null!==l;){if(l.key===c){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Us(o,r.mode,u)).return=r,r=l}return i(r);case O:return e(r,l,(c=o._init)(o._payload),u)}if(te(o))return h(r,l,o,u);if(j(o))return g(r,l,o,u);bl(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Is(o,r.mode,u)).return=r,r=l),i(r)):n(r,l)}}var wl=kl(!0),xl=kl(!1),Sl=Ca(null),El=null,Cl=null,_l=null;function Nl(){_l=Cl=El=null}function Pl(e){var t=Sl.current;_a(Sl),e._currentValue=t}function zl(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ll(e,t){El=e,_l=Cl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(ki=!0),e.firstContext=null)}function Tl(e){var t=e._currentValue;if(_l!==e)if(e={context:e,memoizedValue:t,next:null},null===Cl){if(null===El)throw Error(l(308));Cl=e,El.dependencies={lanes:0,firstContext:e}}else Cl=Cl.next=e;return t}var Ol=null;function Rl(e){null===Ol?Ol=[e]:Ol.push(e)}function Ml(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Rl(t)):(n.next=a.next,a.next=n),t.interleaved=n,jl(e,r)}function jl(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Dl=!1;function Fl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Il(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ul(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $l(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&zu)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,jl(e,n)}return null===(a=r.interleaved)?(t.next=t,Rl(r)):(t.next=a.next,a.next=t),r.interleaved=t,jl(e,n)}function Al(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Vl(e,t,n,r){var a=e.updateQueue;Dl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var u=i,s=u.next;u.next=null,null===o?l=s:o.next=s,o=u;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=s:i.next=s,c.lastBaseUpdate=u))}if(null!==l){var d=a.baseState;for(o=0,c=s=u=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var m=e,h=i;switch(f=t,p=n,h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(p,d,f):m)||void 0===f)break e;d=F({},d,f);break e;case 2:Dl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(s=c=p,u=d):c=c.next=p,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(u=d),a.baseState=u,a.firstBaseUpdate=s,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Fu|=o,e.lanes=o,e.memoizedState=d}}function Wl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var Hl={},Ql=Ca(Hl),ql=Ca(Hl),Kl=Ca(Hl);function Gl(e){if(e===Hl)throw Error(l(174));return e}function Yl(e,t){switch(Na(Kl,t),Na(ql,e),Na(Ql,Hl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ue(null,"");break;default:t=ue(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}_a(Ql),Na(Ql,t)}function Xl(){_a(Ql),_a(ql),_a(Kl)}function Zl(e){Gl(Kl.current);var t=Gl(Ql.current),n=ue(t,e.type);t!==n&&(Na(ql,e),Na(Ql,n))}function Jl(e){ql.current===e&&(_a(Ql),_a(ql))}var eo=Ca(0);function to(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var no=[];function ro(){for(var e=0;e<no.length;e++)no[e]._workInProgressVersionPrimary=null;no.length=0}var ao=k.ReactCurrentDispatcher,lo=k.ReactCurrentBatchConfig,oo=0,io=null,uo=null,so=null,co=!1,fo=!1,po=0,mo=0;function ho(){throw Error(l(321))}function go(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ur(e[n],t[n]))return!1;return!0}function vo(e,t,n,r,a,o){if(oo=o,io=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ao.current=null===e||null===e.memoizedState?ei:ti,e=n(r,a),fo){o=0;do{if(fo=!1,po=0,25<=o)throw Error(l(301));o+=1,so=uo=null,t.updateQueue=null,ao.current=ni,e=n(r,a)}while(fo)}if(ao.current=Jo,t=null!==uo&&null!==uo.next,oo=0,so=uo=io=null,co=!1,t)throw Error(l(300));return e}function bo(){var e=0!==po;return po=0,e}function yo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?io.memoizedState=so=e:so=so.next=e,so}function ko(){if(null===uo){var e=io.alternate;e=null!==e?e.memoizedState:null}else e=uo.next;var t=null===so?io.memoizedState:so.next;if(null!==t)so=t,uo=e;else{if(null===e)throw Error(l(310));e={memoizedState:(uo=e).memoizedState,baseState:uo.baseState,baseQueue:uo.baseQueue,queue:uo.queue,next:null},null===so?io.memoizedState=so=e:so=so.next=e}return so}function wo(e,t){return"function"===typeof t?t(e):t}function xo(e){var t=ko(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=uo,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var u=i=null,s=null,c=o;do{var d=c.lane;if((oo&d)===d)null!==s&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===s?(u=s=f,i=r):s=s.next=f,io.lanes|=d,Fu|=d}c=c.next}while(null!==c&&c!==o);null===s?i=r:s.next=u,ur(r,t.memoizedState)||(ki=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,io.lanes|=o,Fu|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function So(e){var t=ko(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);ur(o,t.memoizedState)||(ki=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Eo(){}function Co(e,t){var n=io,r=ko(),a=t(),o=!ur(r.memoizedState,a);if(o&&(r.memoizedState=a,ki=!0),r=r.queue,Fo(Po.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,Oo(9,No.bind(null,n,r,a,t),void 0,null),null===Lu)throw Error(l(349));0!==(30&oo)||_o(n,t,a)}return a}function _o(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=io.updateQueue)?(t={lastEffect:null,stores:null},io.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function No(e,t,n,r){t.value=n,t.getSnapshot=r,zo(t)&&Lo(e)}function Po(e,t,n){return n(function(){zo(t)&&Lo(e)})}function zo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ur(e,n)}catch(r){return!0}}function Lo(e){var t=jl(e,1);null!==t&&rs(t,e,1,-1)}function To(e){var t=yo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wo,lastRenderedState:e},t.queue=e,e=e.dispatch=Go.bind(null,io,e),[t.memoizedState,e]}function Oo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=io.updateQueue)?(t={lastEffect:null,stores:null},io.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ro(){return ko().memoizedState}function Mo(e,t,n,r){var a=yo();io.flags|=e,a.memoizedState=Oo(1|t,n,void 0,void 0===r?null:r)}function jo(e,t,n,r){var a=ko();r=void 0===r?null:r;var l=void 0;if(null!==uo){var o=uo.memoizedState;if(l=o.destroy,null!==r&&go(r,o.deps))return void(a.memoizedState=Oo(t,n,l,r))}io.flags|=e,a.memoizedState=Oo(1|t,n,l,r)}function Do(e,t){return Mo(8390656,8,e,t)}function Fo(e,t){return jo(2048,8,e,t)}function Io(e,t){return jo(4,2,e,t)}function Uo(e,t){return jo(4,4,e,t)}function $o(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ao(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,jo(4,4,$o.bind(null,t,e),n)}function Bo(){}function Vo(e,t){var n=ko();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&go(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wo(e,t){var n=ko();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&go(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ho(e,t,n){return 0===(21&oo)?(e.baseState&&(e.baseState=!1,ki=!0),e.memoizedState=n):(ur(n,t)||(n=gt(),io.lanes|=n,Fu|=n,e.baseState=!0),t)}function Qo(e,t){var n=kt;kt=0!==n&&4>n?n:4,e(!0);var r=lo.transition;lo.transition={};try{e(!1),t()}finally{kt=n,lo.transition=r}}function qo(){return ko().memoizedState}function Ko(e,t,n){var r=ns(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yo(e))Xo(t,n);else if(null!==(n=Ml(e,t,n,r))){rs(n,e,r,ts()),Zo(n,t,r)}}function Go(e,t,n){var r=ns(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yo(e))Xo(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,ur(i,o)){var u=t.interleaved;return null===u?(a.next=a,Rl(t)):(a.next=u.next,u.next=a),void(t.interleaved=a)}}catch(s){}null!==(n=Ml(e,t,a,r))&&(rs(n,e,r,a=ts()),Zo(n,t,r))}}function Yo(e){var t=e.alternate;return e===io||null!==t&&t===io}function Xo(e,t){fo=co=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Zo(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Jo={readContext:Tl,useCallback:ho,useContext:ho,useEffect:ho,useImperativeHandle:ho,useInsertionEffect:ho,useLayoutEffect:ho,useMemo:ho,useReducer:ho,useRef:ho,useState:ho,useDebugValue:ho,useDeferredValue:ho,useTransition:ho,useMutableSource:ho,useSyncExternalStore:ho,useId:ho,unstable_isNewReconciler:!1},ei={readContext:Tl,useCallback:function(e,t){return yo().memoizedState=[e,void 0===t?null:t],e},useContext:Tl,useEffect:Do,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Mo(4194308,4,$o.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Mo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Mo(4,2,e,t)},useMemo:function(e,t){var n=yo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ko.bind(null,io,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yo().memoizedState=e},useState:To,useDebugValue:Bo,useDeferredValue:function(e){return yo().memoizedState=e},useTransition:function(){var e=To(!1),t=e[0];return e=Qo.bind(null,e[1]),yo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=io,a=yo();if(ll){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Lu)throw Error(l(349));0!==(30&oo)||_o(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Do(Po.bind(null,r,o,e),[e]),r.flags|=2048,Oo(9,No.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=yo(),t=Lu.identifierPrefix;if(ll){var n=Za;t=":"+t+"R"+(n=(Xa&~(1<<32-it(Xa)-1)).toString(32)+n),0<(n=po++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=mo++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ti={readContext:Tl,useCallback:Vo,useContext:Tl,useEffect:Fo,useImperativeHandle:Ao,useInsertionEffect:Io,useLayoutEffect:Uo,useMemo:Wo,useReducer:xo,useRef:Ro,useState:function(){return xo(wo)},useDebugValue:Bo,useDeferredValue:function(e){return Ho(ko(),uo.memoizedState,e)},useTransition:function(){return[xo(wo)[0],ko().memoizedState]},useMutableSource:Eo,useSyncExternalStore:Co,useId:qo,unstable_isNewReconciler:!1},ni={readContext:Tl,useCallback:Vo,useContext:Tl,useEffect:Fo,useImperativeHandle:Ao,useInsertionEffect:Io,useLayoutEffect:Uo,useMemo:Wo,useReducer:So,useRef:Ro,useState:function(){return So(wo)},useDebugValue:Bo,useDeferredValue:function(e){var t=ko();return null===uo?t.memoizedState=e:Ho(t,uo.memoizedState,e)},useTransition:function(){return[So(wo)[0],ko().memoizedState]},useMutableSource:Eo,useSyncExternalStore:Co,useId:qo,unstable_isNewReconciler:!1};function ri(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ai(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var li={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ts(),a=ns(e),l=Ul(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=$l(e,l,a))&&(rs(t,e,a,r),Al(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ts(),a=ns(e),l=Ul(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=$l(e,l,a))&&(rs(t,e,a,r),Al(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ts(),r=ns(e),a=Ul(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=$l(e,a,r))&&(rs(t,e,r,n),Al(t,e,r))}};function oi(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,l))}function ii(e,t,n){var r=!1,a=Pa,l=t.contextType;return"object"===typeof l&&null!==l?l=Tl(l):(a=Ra(t)?Ta:za.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Oa(e,a):Pa),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=li,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function ui(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&li.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fl(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Tl(l):(l=Ra(t)?Ta:za.current,a.context=Oa(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(ai(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&li.enqueueReplaceState(a,a.state,null),Vl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function ci(e,t){try{var n="",r=t;do{n+=A(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function di(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fi(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var pi="function"===typeof WeakMap?WeakMap:Map;function mi(e,t,n){(n=Ul(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hu||(Hu=!0,Qu=r),fi(0,t)},n}function hi(e,t,n){(n=Ul(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){fi(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){fi(0,t),"function"!==typeof r&&(null===qu?qu=new Set([this]):qu.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function gi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new pi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=_s.bind(null,e,t,n),t.then(e,e))}function vi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function bi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ul(-1,1)).tag=2,$l(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var yi=k.ReactCurrentOwner,ki=!1;function wi(e,t,n,r){t.child=null===e?xl(t,null,n,r):wl(t,e.child,n,r)}function xi(e,t,n,r,a){n=n.render;var l=t.ref;return Ll(t,a),r=vo(e,t,n,r,l,a),n=bo(),null===e||ki?(ll&&n&&tl(t),t.flags|=1,wi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hi(e,t,a))}function Si(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Rs(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=js(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ei(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(o,r)&&e.ref===t.ref)return Hi(e,t,a)}return t.flags|=1,(e=Ms(l,r)).ref=t.ref,e.return=t,t.child=e}function Ei(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sr(l,r)&&e.ref===t.ref){if(ki=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Hi(e,t,a);0!==(131072&e.flags)&&(ki=!0)}}return Ni(e,t,n,r,a)}function Ci(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Na(Mu,Ru),Ru|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Na(Mu,Ru),Ru|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Na(Mu,Ru),Ru|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Na(Mu,Ru),Ru|=r;return wi(e,t,a,n),t.child}function _i(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ni(e,t,n,r,a){var l=Ra(n)?Ta:za.current;return l=Oa(t,l),Ll(t,a),n=vo(e,t,n,r,l,a),r=bo(),null===e||ki?(ll&&r&&tl(t),t.flags|=1,wi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hi(e,t,a))}function Pi(e,t,n,r,a){if(Ra(n)){var l=!0;Fa(t)}else l=!1;if(Ll(t,a),null===t.stateNode)Wi(e,t),ii(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var u=o.context,s=n.contextType;"object"===typeof s&&null!==s?s=Tl(s):s=Oa(t,s=Ra(n)?Ta:za.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==r||u!==s)&&ui(t,o,r,s),Dl=!1;var f=t.memoizedState;o.state=f,Vl(t,r,o,a),u=t.memoizedState,i!==r||f!==u||La.current||Dl?("function"===typeof c&&(ai(t,n,c,r),u=t.memoizedState),(i=Dl||oi(t,n,i,r,f,u,s))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=s,r=i):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Il(e,t),i=t.memoizedProps,s=t.type===t.elementType?i:ri(t.type,i),o.props=s,d=t.pendingProps,f=o.context,"object"===typeof(u=n.contextType)&&null!==u?u=Tl(u):u=Oa(t,u=Ra(n)?Ta:za.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==u)&&ui(t,o,r,u),Dl=!1,f=t.memoizedState,o.state=f,Vl(t,r,o,a);var m=t.memoizedState;i!==d||f!==m||La.current||Dl?("function"===typeof p&&(ai(t,n,p,r),m=t.memoizedState),(s=Dl||oi(t,n,s,r,f,m,u)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,m,u),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,m,u)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),o.props=r,o.state=m,o.context=u,r=s):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return zi(e,t,n,r,l,a)}function zi(e,t,n,r,a,l){_i(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Ia(t,n,!1),Hi(e,t,l);r=t.stateNode,yi.current=t;var i=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=wl(t,e.child,null,l),t.child=wl(t,null,i,l)):wi(e,t,i,l),t.memoizedState=r.state,a&&Ia(t,n,!0),t.child}function Li(e){var t=e.stateNode;t.pendingContext?ja(0,t.pendingContext,t.pendingContext!==t.context):t.context&&ja(0,t.context,!1),Yl(e,t.containerInfo)}function Ti(e,t,n,r,a){return ml(),hl(a),t.flags|=256,wi(e,t,n,r),t.child}var Oi,Ri,Mi,ji,Di={dehydrated:null,treeContext:null,retryLane:0};function Fi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ii(e,t,n){var r,a=t.pendingProps,o=eo.current,i=!1,u=0!==(128&t.flags);if((r=u)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Na(eo,1&o),null===e)return cl(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(u=a.children,e=a.fallback,i?(a=t.mode,i=t.child,u={mode:"hidden",children:u},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=u):i=Fs(u,a,0,null),e=Ds(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Fi(n),t.memoizedState=Di,e):Ui(t,u));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,$i(e,t,i,r=di(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Fs({mode:"visible",children:r.children},a,0,null),(o=Ds(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&wl(t,e.child,null,i),t.child.memoizedState=Fi(i),t.memoizedState=Di,o);if(0===(1&t.mode))return $i(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var u=r.dgst;return r=u,$i(e,t,i,r=di(o=Error(l(419)),r,void 0))}if(u=0!==(i&e.childLanes),ki||u){if(null!==(r=Lu)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,jl(e,a),rs(r,e,a,-1))}return gs(),$i(e,t,i,r=di(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Ps.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,al=ca(a.nextSibling),rl=t,ll=!0,ol=null,null!==e&&(Ka[Ga++]=Xa,Ka[Ga++]=Za,Ka[Ga++]=Ya,Xa=e.id,Za=e.overflow,Ya=t),t=Ui(t,r.children),t.flags|=4096,t)}(e,t,u,a,r,o,n);if(i){i=a.fallback,u=t.mode,r=(o=e.child).sibling;var s={mode:"hidden",children:a.children};return 0===(1&u)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=s,t.deletions=null):(a=Ms(o,s)).subtreeFlags=14680064&o.subtreeFlags,null!==r?i=Ms(r,i):(i=Ds(i,u,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,u=null===(u=e.child.memoizedState)?Fi(n):{baseLanes:u.baseLanes|n,cachePool:null,transitions:u.transitions},i.memoizedState=u,i.childLanes=e.childLanes&~n,t.memoizedState=Di,a}return e=(i=e.child).sibling,a=Ms(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ui(e,t){return(t=Fs({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function $i(e,t,n,r){return null!==r&&hl(r),wl(t,e.child,null,n),(e=Ui(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ai(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),zl(e.return,t,n)}function Bi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Vi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(wi(e,t,r.children,n),0!==(2&(r=eo.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ai(e,n,t);else if(19===e.tag)Ai(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Na(eo,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===to(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===to(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bi(t,!0,n,null,l);break;case"together":Bi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fu|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Ms(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ms(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Qi(e,t){if(!ll)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ki(e,t,n){var r=t.pendingProps;switch(nl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qi(t),null;case 1:case 17:return Ra(t.type)&&Ma(),qi(t),null;case 3:return r=t.stateNode,Xl(),_a(La),_a(za),ro(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ol&&(is(ol),ol=null))),Ri(e,t),qi(t),null;case 5:Jl(t);var a=Gl(Kl.current);if(n=t.type,null!==e&&null!=t.stateNode)Mi(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return qi(t),null}if(e=Gl(Ql.current),fl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[pa]=t,r[ma]=o,e=0!==(1&t.mode),n){case"dialog":$r("cancel",r),$r("close",r);break;case"iframe":case"object":case"embed":$r("load",r);break;case"video":case"audio":for(a=0;a<Dr.length;a++)$r(Dr[a],r);break;case"source":$r("error",r);break;case"img":case"image":case"link":$r("error",r),$r("load",r);break;case"details":$r("toggle",r);break;case"input":Y(r,o),$r("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},$r("invalid",r);break;case"textarea":ae(r,o),$r("invalid",r)}for(var u in be(n,o),a=null,o)if(o.hasOwnProperty(u)){var s=o[u];"children"===u?"string"===typeof s?r.textContent!==s&&(!0!==o.suppressHydrationWarning&&Jr(r.textContent,s,e),a=["children",s]):"number"===typeof s&&r.textContent!==""+s&&(!0!==o.suppressHydrationWarning&&Jr(r.textContent,s,e),a=["children",""+s]):i.hasOwnProperty(u)&&null!=s&&"onScroll"===u&&$r("scroll",r)}switch(n){case"input":Q(r),J(r,o,!0);break;case"textarea":Q(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=ea)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{u=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=u.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=u.createElement(n,{is:r.is}):(e=u.createElement(n),"select"===n&&(u=e,r.multiple?u.multiple=!0:r.size&&(u.size=r.size))):e=u.createElementNS(e,n),e[pa]=t,e[ma]=r,Oi(e,t,!1,!1),t.stateNode=e;e:{switch(u=ye(n,r),n){case"dialog":$r("cancel",e),$r("close",e),a=r;break;case"iframe":case"object":case"embed":$r("load",e),a=r;break;case"video":case"audio":for(a=0;a<Dr.length;a++)$r(Dr[a],e);a=r;break;case"source":$r("error",e),a=r;break;case"img":case"image":case"link":$r("error",e),$r("load",e),a=r;break;case"details":$r("toggle",e),a=r;break;case"input":Y(e,r),a=G(e,r),$r("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=F({},r,{value:void 0}),$r("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),$r("invalid",e)}for(o in be(n,a),s=a)if(s.hasOwnProperty(o)){var c=s[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=c&&"onScroll"===o&&$r("scroll",e):null!=c&&y(e,o,c,u))}switch(n){case"input":Q(e),J(e,r,!1);break;case"textarea":Q(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=ea)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qi(t),null;case 6:if(e&&null!=t.stateNode)ji(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Gl(Kl.current),Gl(Ql.current),fl(t)){if(r=t.stateNode,n=t.memoizedProps,r[pa]=t,(o=r.nodeValue!==n)&&null!==(e=rl))switch(e.tag){case 3:Jr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[pa]=t,t.stateNode=r}return qi(t),null;case 13:if(_a(eo),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ll&&null!==al&&0!==(1&t.mode)&&0===(128&t.flags))pl(),ml(),t.flags|=98560,o=!1;else if(o=fl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[pa]=t}else ml(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qi(t),o=!1}else null!==ol&&(is(ol),ol=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&eo.current)?0===ju&&(ju=3):gs())),null!==t.updateQueue&&(t.flags|=4),qi(t),null);case 4:return Xl(),Ri(e,t),null===e&&Vr(t.stateNode.containerInfo),qi(t),null;case 10:return Pl(t.type._context),qi(t),null;case 19:if(_a(eo),null===(o=t.memoizedState))return qi(t),null;if(r=0!==(128&t.flags),null===(u=o.rendering))if(r)Qi(o,!1);else{if(0!==ju||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(u=to(e))){for(t.flags|=128,Qi(o,!1),null!==(r=u.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(u=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=u.childLanes,o.lanes=u.lanes,o.child=u.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=u.memoizedProps,o.memoizedState=u.memoizedState,o.updateQueue=u.updateQueue,o.type=u.type,e=u.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Na(eo,1&eo.current|2),t.child}e=e.sibling}null!==o.tail&&Ze()>Vu&&(t.flags|=128,r=!0,Qi(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=to(u))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Qi(o,!0),null===o.tail&&"hidden"===o.tailMode&&!u.alternate&&!ll)return qi(t),null}else 2*Ze()-o.renderingStartTime>Vu&&1073741824!==n&&(t.flags|=128,r=!0,Qi(o,!1),t.lanes=4194304);o.isBackwards?(u.sibling=t.child,t.child=u):(null!==(n=o.last)?n.sibling=u:t.child=u,o.last=u)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ze(),t.sibling=null,n=eo.current,Na(eo,r?1&n|2:1&n),t):(qi(t),null);case 22:case 23:return fs(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ru)&&(qi(t),6&t.subtreeFlags&&(t.flags|=8192)):qi(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Gi(e,t){switch(nl(t),t.tag){case 1:return Ra(t.type)&&Ma(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xl(),_a(La),_a(za),ro(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Jl(t),null;case 13:if(_a(eo),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));ml()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return _a(eo),null;case 4:return Xl(),null;case 10:return Pl(t.type._context),null;case 22:case 23:return fs(),null;default:return null}}Oi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ri=function(){},Mi=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Gl(Ql.current);var l,o=null;switch(n){case"input":a=G(e,a),r=G(e,r),o=[];break;case"select":a=F({},a,{value:void 0}),r=F({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=ea)}for(c in be(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var u=a[c];for(l in u)u.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var s=r[c];if(u=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&s!==u&&(null!=s||null!=u))if("style"===c)if(u){for(l in u)!u.hasOwnProperty(l)||s&&s.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in s)s.hasOwnProperty(l)&&u[l]!==s[l]&&(n||(n={}),n[l]=s[l])}else n||(o||(o=[]),o.push(c,n)),n=s;else"dangerouslySetInnerHTML"===c?(s=s?s.__html:void 0,u=u?u.__html:void 0,null!=s&&u!==s&&(o=o||[]).push(c,s)):"children"===c?"string"!==typeof s&&"number"!==typeof s||(o=o||[]).push(c,""+s):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=s&&"onScroll"===c&&$r("scroll",e),o||u===s||(o=[])):(o=o||[]).push(c,s))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},ji=function(e,t,n,r){n!==r&&(t.flags|=4)};var Yi=!1,Xi=!1,Zi="function"===typeof WeakSet?WeakSet:Set,Ji=null;function eu(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Cs(e,t,r)}else n.current=null}function tu(e,t,n){try{n()}catch(r){Cs(e,t,r)}}var nu=!1;function ru(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&tu(t,n,l)}a=a.next}while(a!==r)}}function au(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function lu(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ou(e){var t=e.alternate;null!==t&&(e.alternate=null,ou(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[pa],delete t[ma],delete t[ga],delete t[va],delete t[ba])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function iu(e){return 5===e.tag||3===e.tag||4===e.tag}function uu(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||iu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function su(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=ea));else if(4!==r&&null!==(e=e.child))for(su(e,t,n),e=e.sibling;null!==e;)su(e,t,n),e=e.sibling}function cu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cu(e,t,n),e=e.sibling;null!==e;)cu(e,t,n),e=e.sibling}var du=null,fu=!1;function pu(e,t,n){for(n=n.child;null!==n;)mu(e,t,n),n=n.sibling}function mu(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(lt,n)}catch(i){}switch(n.tag){case 5:Xi||eu(n,t);case 6:var r=du,a=fu;du=null,pu(e,t,n),fu=a,null!==(du=r)&&(fu?(e=du,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):du.removeChild(n.stateNode));break;case 18:null!==du&&(fu?(e=du,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Vt(e)):sa(du,n.stateNode));break;case 4:r=du,a=fu,du=n.stateNode.containerInfo,fu=!0,pu(e,t,n),du=r,fu=a;break;case 0:case 11:case 14:case 15:if(!Xi&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(0!==(2&l)||0!==(4&l))&&tu(n,t,o),a=a.next}while(a!==r)}pu(e,t,n);break;case 1:if(!Xi&&(eu(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Cs(n,t,i)}pu(e,t,n);break;case 21:pu(e,t,n);break;case 22:1&n.mode?(Xi=(r=Xi)||null!==n.memoizedState,pu(e,t,n),Xi=r):pu(e,t,n);break;default:pu(e,t,n)}}function hu(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Zi),t.forEach(function(t){var r=zs.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function gu(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,u=i;e:for(;null!==u;){switch(u.tag){case 5:du=u.stateNode,fu=!1;break e;case 3:case 4:du=u.stateNode.containerInfo,fu=!0;break e}u=u.return}if(null===du)throw Error(l(160));mu(o,i,a),du=null,fu=!1;var s=a.alternate;null!==s&&(s.return=null),a.return=null}catch(c){Cs(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vu(t,e),t=t.sibling}function vu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gu(t,e),bu(e),4&r){try{ru(3,e,e.return),au(3,e)}catch(g){Cs(e,e.return,g)}try{ru(5,e,e.return)}catch(g){Cs(e,e.return,g)}}break;case 1:gu(t,e),bu(e),512&r&&null!==n&&eu(n,n.return);break;case 5:if(gu(t,e),bu(e),512&r&&null!==n&&eu(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Cs(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,u=e.type,s=e.updateQueue;if(e.updateQueue=null,null!==s)try{"input"===u&&"radio"===o.type&&null!=o.name&&X(a,o),ye(u,i);var c=ye(u,o);for(i=0;i<s.length;i+=2){var d=s[i],f=s[i+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):y(a,d,f,c)}switch(u){case"input":Z(a,o);break;case"textarea":le(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var m=o.value;null!=m?ne(a,!!o.multiple,m,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[ma]=o}catch(g){Cs(e,e.return,g)}}break;case 6:if(gu(t,e),bu(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){Cs(e,e.return,g)}}break;case 3:if(gu(t,e),bu(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(g){Cs(e,e.return,g)}break;case 4:default:gu(t,e),bu(e);break;case 13:gu(t,e),bu(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Bu=Ze())),4&r&&hu(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xi=(c=Xi)||d,gu(t,e),Xi=c):gu(t,e),bu(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Ji=e,d=e.child;null!==d;){for(f=Ji=d;null!==Ji;){switch(m=(p=Ji).child,p.tag){case 0:case 11:case 14:case 15:ru(4,p,p.return);break;case 1:eu(p,p.return);var h=p.stateNode;if("function"===typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(g){Cs(r,n,g)}}break;case 5:eu(p,p.return);break;case 22:if(null!==p.memoizedState){xu(f);continue}}null!==m?(m.return=p,Ji=m):xu(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(u=f.stateNode,i=void 0!==(s=f.memoizedProps.style)&&null!==s&&s.hasOwnProperty("display")?s.display:null,u.style.display=he("display",i))}catch(g){Cs(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Cs(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:gu(t,e),bu(e),4&r&&hu(e);case 21:}}function bu(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(iu(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),cu(e,uu(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;su(e,uu(e),o);break;default:throw Error(l(161))}}catch(i){Cs(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yu(e,t,n){Ji=e,ku(e,t,n)}function ku(e,t,n){for(var r=0!==(1&e.mode);null!==Ji;){var a=Ji,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Yi;if(!o){var i=a.alternate,u=null!==i&&null!==i.memoizedState||Xi;i=Yi;var s=Xi;if(Yi=o,(Xi=u)&&!s)for(Ji=a;null!==Ji;)u=(o=Ji).child,22===o.tag&&null!==o.memoizedState?Su(a):null!==u?(u.return=o,Ji=u):Su(a);for(;null!==l;)Ji=l,ku(l,t,n),l=l.sibling;Ji=a,Yi=i,Xi=s}wu(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Ji=l):wu(e)}}function wu(e){for(;null!==Ji;){var t=Ji;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xi||au(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ri(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Wl(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wl(t,i,n)}break;case 5:var u=t.stateNode;if(null===n&&4&t.flags){n=u;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Vt(f)}}}break;default:throw Error(l(163))}Xi||512&t.flags&&lu(t)}catch(ze){Cs(t,t.return,ze)}}if(t===e){Ji=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ji=n;break}Ji=t.return}}function xu(e){for(;null!==Ji;){var t=Ji;if(t===e){Ji=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ji=n;break}Ji=t.return}}function Su(e){for(;null!==Ji;){var t=Ji;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{au(4,t)}catch(u){Cs(t,n,u)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(u){Cs(t,a,u)}}var l=t.return;try{lu(t)}catch(u){Cs(t,l,u)}break;case 5:var o=t.return;try{lu(t)}catch(u){Cs(t,o,u)}}}catch(u){Cs(t,t.return,u)}if(t===e){Ji=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Ji=i;break}Ji=t.return}}var Eu,Cu=Math.ceil,_u=k.ReactCurrentDispatcher,Nu=k.ReactCurrentOwner,Pu=k.ReactCurrentBatchConfig,zu=0,Lu=null,Tu=null,Ou=0,Ru=0,Mu=Ca(0),ju=0,Du=null,Fu=0,Iu=0,Uu=0,$u=null,Au=null,Bu=0,Vu=1/0,Wu=null,Hu=!1,Qu=null,qu=null,Ku=!1,Gu=null,Yu=0,Xu=0,Zu=null,Ju=-1,es=0;function ts(){return 0!==(6&zu)?Ze():-1!==Ju?Ju:Ju=Ze()}function ns(e){return 0===(1&e.mode)?1:0!==(2&zu)&&0!==Ou?Ou&-Ou:null!==gl.transition?(0===es&&(es=gt()),es):0!==(e=kt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function rs(e,t,n,r){if(50<Xu)throw Xu=0,Zu=null,Error(l(185));bt(e,n,r),0!==(2&zu)&&e===Lu||(e===Lu&&(0===(2&zu)&&(Iu|=n),4===ju&&us(e,Ou)),as(e,r),1===n&&0===zu&&0===(1&t.mode)&&(Vu=Ze()+500,$a&&Va()))}function as(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-it(l),i=1<<o,u=a[o];-1===u?0!==(i&n)&&0===(i&r)||(a[o]=mt(i,t)):u<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=pt(e,e===Lu?Ou:0);if(0===r)null!==n&&Ge(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ge(n),1===t)0===e.tag?function(e){$a=!0,Ba(e)}(ss.bind(null,e)):Ba(ss.bind(null,e)),ia(function(){0===(6&zu)&&Va()}),n=null;else{switch(wt(r)){case 1:n=et;break;case 4:n=tt;break;case 16:default:n=nt;break;case 536870912:n=at}n=Ls(n,ls.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ls(e,t){if(Ju=-1,es=0,0!==(6&zu))throw Error(l(327));var n=e.callbackNode;if(Ss()&&e.callbackNode!==n)return null;var r=pt(e,e===Lu?Ou:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=vs(e,r);else{t=r;var a=zu;zu|=2;var o=hs();for(Lu===e&&Ou===t||(Wu=null,Vu=Ze()+500,ps(e,t));;)try{ys();break}catch(u){ms(e,u)}Nl(),_u.current=o,zu=a,null!==Tu?t=0:(Lu=null,Ou=0,t=ju)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=os(e,a))),1===t)throw n=Du,ps(e,0),us(e,r),as(e,Ze()),n;if(6===t)us(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!ur(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=vs(e,r))&&(0!==(o=ht(e))&&(r=o,t=os(e,o))),1===t))throw n=Du,ps(e,0),us(e,r),as(e,Ze()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:xs(e,Au,Wu);break;case 3:if(us(e,r),(130023424&r)===r&&10<(t=Bu+500-Ze())){if(0!==pt(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ts(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=aa(xs.bind(null,e,Au,Wu),t);break}xs(e,Au,Wu);break;case 4:if(us(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-it(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=Ze()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Cu(r/1960))-r)){e.timeoutHandle=aa(xs.bind(null,e,Au,Wu),r);break}xs(e,Au,Wu);break;default:throw Error(l(329))}}}return as(e,Ze()),e.callbackNode===n?ls.bind(null,e):null}function os(e,t){var n=$u;return e.current.memoizedState.isDehydrated&&(ps(e,t).flags|=256),2!==(e=vs(e,t))&&(t=Au,Au=n,null!==t&&is(t)),e}function is(e){null===Au?Au=e:Au.push.apply(Au,e)}function us(e,t){for(t&=~Uu,t&=~Iu,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function ss(e){if(0!==(6&zu))throw Error(l(327));Ss();var t=pt(e,0);if(0===(1&t))return as(e,Ze()),null;var n=vs(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=os(e,r))}if(1===n)throw n=Du,ps(e,0),us(e,t),as(e,Ze()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xs(e,Au,Wu),as(e,Ze()),null}function cs(e,t){var n=zu;zu|=1;try{return e(t)}finally{0===(zu=n)&&(Vu=Ze()+500,$a&&Va())}}function ds(e){null!==Gu&&0===Gu.tag&&0===(6&zu)&&Ss();var t=zu;zu|=1;var n=Pu.transition,r=kt;try{if(Pu.transition=null,kt=1,e)return e()}finally{kt=r,Pu.transition=n,0===(6&(zu=t))&&Va()}}function fs(){Ru=Mu.current,_a(Mu)}function ps(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,la(n)),null!==Tu)for(n=Tu.return;null!==n;){var r=n;switch(nl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ma();break;case 3:Xl(),_a(La),_a(za),ro();break;case 5:Jl(r);break;case 4:Xl();break;case 13:case 19:_a(eo);break;case 10:Pl(r.type._context);break;case 22:case 23:fs()}n=n.return}if(Lu=e,Tu=e=Ms(e.current,null),Ou=Ru=t,ju=0,Du=null,Uu=Iu=Fu=0,Au=$u=null,null!==Ol){for(t=0;t<Ol.length;t++)if(null!==(r=(n=Ol[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}Ol=null}return e}function ms(e,t){for(;;){var n=Tu;try{if(Nl(),ao.current=Jo,co){for(var r=io.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}co=!1}if(oo=0,so=uo=io=null,fo=!1,po=0,Nu.current=null,null===n||null===n.return){ju=1,Du=t,Tu=null;break}e:{var o=e,i=n.return,u=n,s=t;if(t=Ou,u.flags|=32768,null!==s&&"object"===typeof s&&"function"===typeof s.then){var c=s,d=u,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=vi(i);if(null!==m){m.flags&=-257,bi(m,i,u,0,t),1&m.mode&&gi(o,c,t),s=c;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(s),t.updateQueue=g}else h.add(s);break e}if(0===(1&t)){gi(o,c,t),gs();break e}s=Error(l(426))}else if(ll&&1&u.mode){var v=vi(i);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),bi(v,i,u,0,t),hl(ci(s,u));break e}}o=s=ci(s,u),4!==ju&&(ju=2),null===$u?$u=[o]:$u.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Bl(o,mi(0,s,t));break e;case 1:u=s;var b=o.type,y=o.stateNode;if(0===(128&o.flags)&&("function"===typeof b.getDerivedStateFromError||null!==y&&"function"===typeof y.componentDidCatch&&(null===qu||!qu.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t,Bl(o,hi(o,u,t));break e}}o=o.return}while(null!==o)}ws(n)}catch(k){t=k,Tu===n&&null!==n&&(Tu=n=n.return);continue}break}}function hs(){var e=_u.current;return _u.current=Jo,null===e?Jo:e}function gs(){0!==ju&&3!==ju&&2!==ju||(ju=4),null===Lu||0===(268435455&Fu)&&0===(268435455&Iu)||us(Lu,Ou)}function vs(e,t){var n=zu;zu|=2;var r=hs();for(Lu===e&&Ou===t||(Wu=null,ps(e,t));;)try{bs();break}catch(a){ms(e,a)}if(Nl(),zu=n,_u.current=r,null!==Tu)throw Error(l(261));return Lu=null,Ou=0,ju}function bs(){for(;null!==Tu;)ks(Tu)}function ys(){for(;null!==Tu&&!Ye();)ks(Tu)}function ks(e){var t=Eu(e.alternate,e,Ru);e.memoizedProps=e.pendingProps,null===t?ws(e):Tu=t,Nu.current=null}function ws(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ki(n,t,Ru)))return void(Tu=n)}else{if(null!==(n=Gi(n,t)))return n.flags&=32767,void(Tu=n);if(null===e)return ju=6,void(Tu=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Tu=t);Tu=t=e}while(null!==t);0===ju&&(ju=5)}function xs(e,t,n){var r=kt,a=Pu.transition;try{Pu.transition=null,kt=1,function(e,t,n,r){do{Ss()}while(null!==Gu);if(0!==(6&zu))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-it(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===Lu&&(Tu=Lu=null,Ou=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ku||(Ku=!0,Ls(nt,function(){return Ss(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Pu.transition,Pu.transition=null;var i=kt;kt=1;var u=zu;zu|=4,Nu.current=null,function(e,t){if(ta=Ht,mr(e=pr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var i=0,u=-1,s=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(u=i+a),f!==o||0!==r&&3!==f.nodeType||(s=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(u=i),p===o&&++d===r&&(s=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===u||-1===s?null:{start:u,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(na={focusedElem:e,selectionRange:n},Ht=!1,Ji=t;null!==Ji;)if(e=(t=Ji).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Ji=e;else for(;null!==Ji;){t=Ji;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,b=t.stateNode,y=b.getSnapshotBeforeUpdate(t.elementType===t.type?g:ri(t.type,g),v);b.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var k=t.stateNode.containerInfo;1===k.nodeType?k.textContent="":9===k.nodeType&&k.documentElement&&k.removeChild(k.documentElement);break;default:throw Error(l(163))}}catch(w){Cs(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Ji=e;break}Ji=t.return}h=nu,nu=!1}(e,n),vu(n,e),hr(na),Ht=!!ta,na=ta=null,e.current=n,yu(n,e,a),Xe(),zu=u,kt=i,Pu.transition=o}else e.current=n;if(Ku&&(Ku=!1,Gu=e,Yu=a),o=e.pendingLanes,0===o&&(qu=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(lt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),as(e,Ze()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Hu)throw Hu=!1,e=Qu,Qu=null,e;0!==(1&Yu)&&0!==e.tag&&Ss(),o=e.pendingLanes,0!==(1&o)?e===Zu?Xu++:(Xu=0,Zu=e):Xu=0,Va()}(e,t,n,r)}finally{Pu.transition=a,kt=r}return null}function Ss(){if(null!==Gu){var e=wt(Yu),t=Pu.transition,n=kt;try{if(Pu.transition=null,kt=16>e?16:e,null===Gu)var r=!1;else{if(e=Gu,Gu=null,Yu=0,0!==(6&zu))throw Error(l(331));var a=zu;for(zu|=4,Ji=e.current;null!==Ji;){var o=Ji,i=o.child;if(0!==(16&Ji.flags)){var u=o.deletions;if(null!==u){for(var s=0;s<u.length;s++){var c=u[s];for(Ji=c;null!==Ji;){var d=Ji;switch(d.tag){case 0:case 11:case 15:ru(8,d,o)}var f=d.child;if(null!==f)f.return=d,Ji=f;else for(;null!==Ji;){var p=(d=Ji).sibling,m=d.return;if(ou(d),d===c){Ji=null;break}if(null!==p){p.return=m,Ji=p;break}Ji=m}}}var h=o.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Ji=o}}if(0!==(2064&o.subtreeFlags)&&null!==i)i.return=o,Ji=i;else e:for(;null!==Ji;){if(0!==(2048&(o=Ji).flags))switch(o.tag){case 0:case 11:case 15:ru(9,o,o.return)}var b=o.sibling;if(null!==b){b.return=o.return,Ji=b;break e}Ji=o.return}}var y=e.current;for(Ji=y;null!==Ji;){var k=(i=Ji).child;if(0!==(2064&i.subtreeFlags)&&null!==k)k.return=i,Ji=k;else e:for(i=y;null!==Ji;){if(0!==(2048&(u=Ji).flags))try{switch(u.tag){case 0:case 11:case 15:au(9,u)}}catch(x){Cs(u,u.return,x)}if(u===i){Ji=null;break e}var w=u.sibling;if(null!==w){w.return=u.return,Ji=w;break e}Ji=u.return}}if(zu=a,Va(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(lt,e)}catch(x){}r=!0}return r}finally{kt=n,Pu.transition=t}}return!1}function Es(e,t,n){e=$l(e,t=mi(0,t=ci(n,t),1),1),t=ts(),null!==e&&(bt(e,1,t),as(e,t))}function Cs(e,t,n){if(3===e.tag)Es(e,e,n);else for(;null!==t;){if(3===t.tag){Es(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qu||!qu.has(r))){t=$l(t,e=hi(t,e=ci(n,e),1),1),e=ts(),null!==t&&(bt(t,1,e),as(t,e));break}}t=t.return}}function _s(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ts(),e.pingedLanes|=e.suspendedLanes&n,Lu===e&&(Ou&n)===n&&(4===ju||3===ju&&(130023424&Ou)===Ou&&500>Ze()-Bu?ps(e,0):Uu|=n),as(e,t)}function Ns(e,t){0===t&&(0===(1&e.mode)?t=1:(t=dt,0===(130023424&(dt<<=1))&&(dt=4194304)));var n=ts();null!==(e=jl(e,t))&&(bt(e,t,n),as(e,n))}function Ps(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ns(e,n)}function zs(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Ns(e,n)}function Ls(e,t){return Ke(e,t)}function Ts(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Os(e,t,n,r){return new Ts(e,t,n,r)}function Rs(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ms(e,t){var n=e.alternate;return null===n?((n=Os(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function js(e,t,n,r,a,o){var i=2;if(r=e,"function"===typeof e)Rs(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case S:return Ds(n.children,a,o,t);case E:i=8,a|=8;break;case C:return(e=Os(12,n,t,2|a)).elementType=C,e.lanes=o,e;case z:return(e=Os(13,n,t,a)).elementType=z,e.lanes=o,e;case L:return(e=Os(19,n,t,a)).elementType=L,e.lanes=o,e;case R:return Fs(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case _:i=10;break e;case N:i=9;break e;case P:i=11;break e;case T:i=14;break e;case O:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Os(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Ds(e,t,n,r){return(e=Os(7,e,r,t)).lanes=n,e}function Fs(e,t,n,r){return(e=Os(22,e,r,t)).elementType=R,e.lanes=n,e.stateNode={isHidden:!1},e}function Is(e,t,n){return(e=Os(6,e,null,t)).lanes=n,e}function Us(e,t,n){return(t=Os(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function $s(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function As(e,t,n,r,a,l,o,i,u){return e=new $s(e,t,n,i,u),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Os(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fl(l),e}function Bs(e){if(!e)return Pa;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ra(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Ra(n))return Da(e,n,t)}return t}function Vs(e,t,n,r,a,l,o,i,u){return(e=As(n,r,!0,e,0,l,0,i,u)).context=Bs(null),n=e.current,(l=Ul(r=ts(),a=ns(n))).callback=void 0!==t&&null!==t?t:null,$l(n,l,a),e.current.lanes=a,bt(e,a,r),as(e,r),e}function Ws(e,t,n,r){var a=t.current,l=ts(),o=ns(a);return n=Bs(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ul(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=$l(a,t,o))&&(rs(e,a,o,l),Al(e,a,o)),o}function Hs(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Qs(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qs(e,t){Qs(e,t),(e=e.alternate)&&Qs(e,t)}Eu=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||La.current)ki=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return ki=!1,function(e,t,n){switch(t.tag){case 3:Li(t),ml();break;case 5:Zl(t);break;case 1:Ra(t.type)&&Fa(t);break;case 4:Yl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Na(Sl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Na(eo,1&eo.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ii(e,t,n):(Na(eo,1&eo.current),null!==(e=Hi(e,t,n))?e.sibling:null);Na(eo,1&eo.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Vi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Na(eo,eo.current),r)break;return null;case 22:case 23:return t.lanes=0,Ci(e,t,n)}return Hi(e,t,n)}(e,t,n);ki=0!==(131072&e.flags)}else ki=!1,ll&&0!==(1048576&t.flags)&&el(t,qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wi(e,t),e=t.pendingProps;var a=Oa(t,za.current);Ll(t,n),a=vo(null,t,r,e,a,n);var o=bo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ra(r)?(o=!0,Fa(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fl(t),a.updater=li,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=zi(null,t,r,!0,o,n)):(t.tag=0,ll&&o&&tl(t),wi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Rs(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===T)return 14}return 2}(r),e=ri(r,e),a){case 0:t=Ni(null,t,r,e,n);break e;case 1:t=Pi(null,t,r,e,n);break e;case 11:t=xi(null,t,r,e,n);break e;case 14:t=Si(null,t,r,ri(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ni(e,t,r,a=t.elementType===r?a:ri(r,a),n);case 1:return r=t.type,a=t.pendingProps,Pi(e,t,r,a=t.elementType===r?a:ri(r,a),n);case 3:e:{if(Li(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Il(e,t),Vl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ti(e,t,r,n,a=ci(Error(l(423)),t));break e}if(r!==a){t=Ti(e,t,r,n,a=ci(Error(l(424)),t));break e}for(al=ca(t.stateNode.containerInfo.firstChild),rl=t,ll=!0,ol=null,n=xl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ml(),r===a){t=Hi(e,t,n);break e}wi(e,t,r,n)}t=t.child}return t;case 5:return Zl(t),null===e&&cl(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,ra(r,a)?i=null:null!==o&&ra(r,o)&&(t.flags|=32),_i(e,t),wi(e,t,i,n),t.child;case 6:return null===e&&cl(t),null;case 13:return Ii(e,t,n);case 4:return Yl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wl(t,null,r,n):wi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,xi(e,t,r,a=t.elementType===r?a:ri(r,a),n);case 7:return wi(e,t,t.pendingProps,n),t.child;case 8:case 12:return wi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Na(Sl,r._currentValue),r._currentValue=i,null!==o)if(ur(o.value,i)){if(o.children===a.children&&!La.current){t=Hi(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var u=o.dependencies;if(null!==u){i=o.child;for(var s=u.firstContext;null!==s;){if(s.context===r){if(1===o.tag){(s=Ul(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?s.next=s:(s.next=d.next,d.next=s),c.pending=s}}o.lanes|=n,null!==(s=o.alternate)&&(s.lanes|=n),zl(o.return,n,t),u.lanes|=n;break}s=s.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),zl(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}wi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ll(t,n),r=r(a=Tl(a)),t.flags|=1,wi(e,t,r,n),t.child;case 14:return a=ri(r=t.type,t.pendingProps),Si(e,t,r,a=ri(r.type,a),n);case 15:return Ei(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ri(r,a),Wi(e,t),t.tag=1,Ra(r)?(e=!0,Fa(t)):e=!1,Ll(t,n),ii(t,r,a),si(t,r,a,n),zi(null,t,r,!0,e,n);case 19:return Vi(e,t,n);case 22:return Ci(e,t,n)}throw Error(l(156,t.tag))};var Ks="function"===typeof reportError?reportError:function(e){console.error(e)};function Gs(e){this._internalRoot=e}function Ys(e){this._internalRoot=e}function Xs(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Zs(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Js(){}function ec(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"===typeof a){var i=a;a=function(){var e=Hs(o);i.call(e)}}Ws(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=Hs(o);l.call(e)}}var o=Vs(t,r,e,0,null,!1,0,"",Js);return e._reactRootContainer=o,e[ha]=o.current,Vr(8===e.nodeType?e.parentNode:e),ds(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Hs(u);i.call(e)}}var u=As(e,0,!1,null,0,!1,0,"",Js);return e._reactRootContainer=u,e[ha]=u.current,Vr(8===e.nodeType?e.parentNode:e),ds(function(){Ws(t,u,n,r)}),u}(n,t,e,a,r);return Hs(o)}Ys.prototype.render=Gs.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Ws(e,t,null,null)},Ys.prototype.unmount=Gs.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;ds(function(){Ws(null,e,null,null)}),t[ha]=null}},Ys.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ct();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Mt.length&&0!==t&&t<Mt[n].priority;n++);Mt.splice(n,0,e),0===n&&It(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ft(t.pendingLanes);0!==n&&(yt(t,1|n),as(t,Ze()),0===(6&zu)&&(Vu=Ze()+500,Va()))}break;case 13:ds(function(){var t=jl(e,1);if(null!==t){var n=ts();rs(t,e,1,n)}}),qs(e,1)}},St=function(e){if(13===e.tag){var t=jl(e,134217728);if(null!==t)rs(t,e,134217728,ts());qs(e,134217728)}},Et=function(e){if(13===e.tag){var t=ns(e),n=jl(e,t);if(null!==n)rs(n,e,t,ts());qs(e,t)}},Ct=function(){return kt},_t=function(e,t){var n=kt;try{return kt=e,t()}finally{kt=n}},xe=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=xa(r);if(!a)throw Error(l(90));q(r),Z(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=cs,Le=ds;var tc={usingClientEntryPoint:!1,Events:[ka,wa,xa,_e,Ne,cs]},nc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:k.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Qe(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ac=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ac.isDisabled&&ac.supportsFiber)try{lt=ac.inject(rc),ot=ac}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xs(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:x,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xs(e))throw Error(l(299));var n=!1,r="",a=Ks;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=As(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Vr(8===e.nodeType?e.parentNode:e),new Gs(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=Qe(t))?null:e.stateNode},t.flushSync=function(e){return ds(e)},t.hydrate=function(e,t,n){if(!Zs(t))throw Error(l(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xs(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=Ks;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Vs(t,null,e,1,null!=n?n:null,a,0,o,i),e[ha]=t.current,Vr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Ys(t)},t.render=function(e,t,n){if(!Zs(t))throw Error(l(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Zs(e))throw Error(l(40));return!!e._reactRootContainer&&(ds(function(){ec(null,null,e,!1,function(){e._reactRootContainer=null,e[ha]=null})}),!0)},t.unstable_batchedUpdates=cs,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Zs(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return ec(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var o={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach(e=>o[e]=()=>r[e]);return o.default=()=>r,n.d(l,o),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r,a=n(43),l=n.t(a,2),o=n(391),i=n(950),u=n.t(i,2);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const c="popstate";function d(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function f(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function p(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,n,r){return void 0===n&&(n=null),s({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?g(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function g(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v(e,t,n,a){void 0===a&&(a={});let{window:l=document.defaultView,v5Compat:o=!1}=a,i=l.history,u=r.Pop,f=null,g=v();function v(){return(i.state||{idx:null}).idx}function b(){u=r.Pop;let e=v(),t=null==e?null:e-g;g=e,f&&f({action:u,location:k.location,delta:t})}function y(e){let t="null"!==l.location.origin?l.location.origin:l.location.href,n="string"===typeof e?e:h(e);return n=n.replace(/ $/,"%20"),d(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==g&&(g=0,i.replaceState(s({},i.state,{idx:g}),""));let k={get action(){return u},get location(){return e(l,i)},listen(e){if(f)throw new Error("A history only accepts one active listener");return l.addEventListener(c,b),f=e,()=>{l.removeEventListener(c,b),f=null}},createHref:e=>t(l,e),createURL:y,encodeLocation(e){let t=y(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){u=r.Push;let a=m(k.location,e,t);n&&n(a,e),g=v()+1;let s=p(a,g),c=k.createHref(a);try{i.pushState(s,"",c)}catch(d){if(d instanceof DOMException&&"DataCloneError"===d.name)throw d;l.location.assign(c)}o&&f&&f({action:u,location:k.location,delta:1})},replace:function(e,t){u=r.Replace;let a=m(k.location,e,t);n&&n(a,e),g=v();let l=p(a,g),s=k.createHref(a);i.replaceState(l,"",s),o&&f&&f({action:u,location:k.location,delta:0})},go:e=>i.go(e)};return k}var b;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(b||(b={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function y(e,t,n){return void 0===n&&(n="/"),k(e,t,n,!1)}function k(e,t,n,r){let a=M(("string"===typeof t?g(t):t).pathname||"/",n);if(null==a)return null;let l=w(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(l);let o=null;for(let i=0;null==o&&i<l.length;++i){let e=R(a);o=T(l[i],e,r)}return o}function w(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let o={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(d(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let i=j([r,o.relativePath]),u=n.concat(o);e.children&&e.children.length>0&&(d(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),w(e.children,t,u,i)),(null!=e.path||e.index)&&t.push({path:i,score:L(i,e.index),routesMeta:u})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of x(e.path))a(e,t,r);else a(e,t)}),t}function x(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let o=x(r.join("/")),i=[];return i.push(...o.map(e=>""===e?l:[l,e].join("/"))),a&&i.push(...o),i.map(t=>e.startsWith("/")&&""===t?"/":t)}const S=/^:[\w-]+$/,E=3,C=2,_=1,N=10,P=-2,z=e=>"*"===e;function L(e,t){let n=e.split("/"),r=n.length;return n.some(z)&&(r+=P),t&&(r+=C),n.filter(e=>!z(e)).reduce((e,t)=>e+(S.test(t)?E:""===t?_:N),r)}function T(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",o=[];for(let i=0;i<r.length;++i){let e=r[i],u=i===r.length-1,s="/"===l?t:t.slice(l.length)||"/",c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),d=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:j([l,c.pathname]),pathnameBase:D(j([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=j([l,c.pathnameBase]))}return o}function O(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);f("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],o=l.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";o=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const u=i[n];return e[r]=a&&!u?void 0:(u||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:o,pattern:e}}function R(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return f(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function M(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}const j=e=>e.join("/").replace(/\/\/+/g,"/"),D=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");Error;function F(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const I=["post","put","patch","delete"],U=(new Set(I),["get",...I]);new Set(U),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$.apply(this,arguments)}const A=a.createContext(null);const B=a.createContext(null);const V=a.createContext(null);const W=a.createContext(null);const H=a.createContext({outlet:null,matches:[],isDataRoute:!1});const Q=a.createContext(null);function q(){return null!=a.useContext(W)}function K(){return q()||d(!1),a.useContext(W).location}function G(e,t,n,l){q()||d(!1);let{navigator:o}=a.useContext(V),{matches:i}=a.useContext(H),u=i[i.length-1],s=u?u.params:{},c=(u&&u.pathname,u?u.pathnameBase:"/");u&&u.route;let f,p=K();if(t){var m;let e="string"===typeof t?g(t):t;"/"===c||(null==(m=e.pathname)?void 0:m.startsWith(c))||d(!1),f=e}else f=p;let h=f.pathname||"/",v=h;if("/"!==c){let e=c.replace(/^\//,"").split("/");v="/"+h.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=y(e,{pathname:v});let k=ee(b&&b.map(e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:j([c,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:j([c,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,l);return t&&k?a.createElement(W.Provider,{value:{location:$({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:r.Pop}},k):k}function Y(){let e=function(){var e;let t=a.useContext(Q),n=ne(te.UseRouteError),r=re(te.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=F(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:l},n):null,null)}const X=a.createElement(Y,null);class Z extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(H.Provider,{value:this.props.routeContext},a.createElement(Q.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function J(e){let{routeContext:t,match:n,children:r}=e,l=a.useContext(A);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(H.Provider,{value:t},r)}function ee(e,t,n,r){var l;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,u=null==(l=n)?void 0:l.errors;if(null!=u){let e=i.findIndex(e=>e.route.id&&void 0!==(null==u?void 0:u[e.route.id]));e>=0||d(!1),i=i.slice(0,Math.min(i.length,e+1))}let s=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){s=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight((e,r,l)=>{let o,d=!1,f=null,p=null;var m;n&&(o=u&&r.route.id?u[r.route.id]:void 0,f=r.route.errorElement||X,s&&(c<0&&0===l?(m="route-fallback",!1||ae[m]||(ae[m]=!0),d=!0,p=null):c===l&&(d=!0,p=r.route.hydrateFallbackElement||null)));let h=t.concat(i.slice(0,l+1)),g=()=>{let t;return t=o?f:d?p:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(J,{match:r,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===l)?a.createElement(Z,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):g()},null)}var te=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(te||{});function ne(e){let t=a.useContext(B);return t||d(!1),t}function re(e){let t=function(){let e=a.useContext(H);return e||d(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||d(!1),n.route.id}const ae={};function le(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}l.startTransition;function oe(e){d(!1)}function ie(e){let{basename:t="/",children:n=null,location:l,navigationType:o=r.Pop,navigator:i,static:u=!1,future:s}=e;q()&&d(!1);let c=t.replace(/^\/*/,"/"),f=a.useMemo(()=>({basename:c,navigator:i,static:u,future:$({v7_relativeSplatPath:!1},s)}),[c,s,i,u]);"string"===typeof l&&(l=g(l));let{pathname:p="/",search:m="",hash:h="",state:v=null,key:b="default"}=l,y=a.useMemo(()=>{let e=M(p,c);return null==e?null:{location:{pathname:e,search:m,hash:h,state:v,key:b},navigationType:o}},[c,p,m,h,v,b,o]);return null==y?null:a.createElement(V.Provider,{value:f},a.createElement(W.Provider,{children:n,value:y}))}function ue(e){let{children:t,location:n}=e;return G(se(t),n)}new Promise(()=>{});a.Component;function se(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,(e,r)=>{if(!a.isValidElement(e))return;let l=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,se(e.props.children,l));e.type!==oe&&d(!1),e.props.index&&e.props.children&&d(!1);let o={id:e.props.id||l.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=se(e.props.children,l)),n.push(o)}),n}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(nn){}new Map;const ce=l.startTransition;u.flushSync,l.useId;function de(e){let{basename:t,children:n,future:r,window:l}=e,o=a.useRef();var i;null==o.current&&(o.current=(void 0===(i={window:l,v5Compat:!0})&&(i={}),v(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return m("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:h(t)},null,i)));let u=o.current,[s,c]=a.useState({action:u.action,location:u.location}),{v7_startTransition:d}=r||{},f=a.useCallback(e=>{d&&ce?ce(()=>c(e)):c(e)},[c,d]);return a.useLayoutEffect(()=>u.listen(f),[u,f]),a.useEffect(()=>le(r),[r]),a.createElement(ie,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:u,future:r})}"undefined"!==typeof window&&"undefined"!==typeof window.document&&window.document.createElement;var fe,pe;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(fe||(fe={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(pe||(pe={}));function me(e){return me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},me(e)}function he(e){var t=function(e,t){if("object"!=me(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=me(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==me(t)?t:t+""}function ge(e,t,n){return(t=he(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function be(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(n),!0).forEach(function(t){ge(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ve(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ye(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const ke=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var we={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const xe=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],Se=(0,a.forwardRef)((e,t)=>{let{color:n="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:i="",children:u,iconNode:s}=e,c=ye(e,xe);return(0,a.createElement)("svg",be(be({ref:t},we),{},{width:r,height:r,stroke:n,strokeWidth:o?24*Number(l)/Number(r):l,className:ke("lucide",i)},c),[...s.map(e=>{let[t,n]=e;return(0,a.createElement)(t,n)}),...Array.isArray(u)?u:[u]])}),Ee=["className"],Ce=(e,t)=>{const n=(0,a.forwardRef)((n,r)=>{let{className:l}=n,o=ye(n,Ee);return(0,a.createElement)(Se,be({ref:r,iconNode:t,className:ke("lucide-".concat((i=e,i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),l)},o));var i});return n.displayName="".concat(e),n},_e=Ce("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),Ne=Ce("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),Pe=Ce("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);function ze(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=ze(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Le(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=ze(e))&&(r&&(r+=" "),r+=t);return r}const Te=(e,t)=>{var n;if(0===e.length)return t.classGroupId;const r=e[0],a=t.nextPart.get(r),l=a?Te(e.slice(1),a):void 0;if(l)return l;if(0===t.validators.length)return;const o=e.join("-");return null===(n=t.validators.find(e=>{let{validator:t}=e;return t(o)}))||void 0===n?void 0:n.classGroupId},Oe=/^\[(.+)\]$/,Re=e=>{if(Oe.test(e)){const t=Oe.exec(e)[1],n=null===t||void 0===t?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Me=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const a in n)je(n[a],r,a,t);return r},je=(e,t,n,r)=>{e.forEach(e=>{if("string"===typeof e){return void((""===e?t:De(t,e)).classGroupId=n)}if("function"===typeof e)return Fe(e)?void je(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(e=>{let[a,l]=e;je(l,De(t,a),n,r)})})},De=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},Fe=e=>e.isThemeGetter,Ie=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const a=(a,l)=>{n.set(a,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(a(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):a(e,t)}}},Ue=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=e=>{const t=[];let n,r=0,a=0,l=0;for(let u=0;u<e.length;u++){let o=e[u];if(0===r&&0===a){if(":"===o){t.push(e.slice(l,u)),l=u+1;continue}if("/"===o){n=u;continue}}"["===o?r++:"]"===o?r--:"("===o?a++:")"===o&&a--}const o=0===t.length?e:e.substring(l),i=$e(o);return{modifiers:t,hasImportantModifier:i!==o,baseClassName:i,maybePostfixModifierPosition:n&&n>l?n-l:void 0}};if(t){const e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){const e=r;r=t=>n({className:t,parseClassName:e})}return r},$e=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,Ae=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;const n=[];let r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},Be=e=>be({cache:Ie(e.cacheSize),parseClassName:Ue(e),sortModifiers:Ae(e)},(e=>{const t=Me(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{const n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),Te(n,t)||Re(e)},getConflictingClassGroupIds:(e,t)=>{const a=n[e]||[];return t&&r[e]?[...a,...r[e]]:a}}})(e)),Ve=/\s+/;function We(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=He(e))&&(r&&(r+=" "),r+=t);return r}const He=e=>{if("string"===typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=He(e[r]))&&(n&&(n+=" "),n+=t);return n};function Qe(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let a,l,o,i=function(t){const r=n.reduce((e,t)=>t(e),e());return a=Be(r),l=a.cache.get,o=a.cache.set,i=u,u(t)};function u(e){const t=l(e);if(t)return t;const n=((e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:a,sortModifiers:l}=t,o=[],i=e.trim().split(Ve);let u="";for(let s=i.length-1;s>=0;s-=1){const e=i[s],{isExternal:t,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=n(e);if(t){u=e+(u.length>0?" "+u:u);continue}let m=!!p,h=r(m?f.substring(0,p):f);if(!h){if(!m){u=e+(u.length>0?" "+u:u);continue}if(h=r(f),!h){u=e+(u.length>0?" "+u:u);continue}m=!1}const g=l(c).join(":"),v=d?g+"!":g,b=v+h;if(o.includes(b))continue;o.push(b);const y=a(h,m);for(let n=0;n<y.length;++n){const e=y[n];o.push(v+e)}u=e+(u.length>0?" "+u:u)}return u})(e,a);return o(e,n),n}return function(){return i(We.apply(null,arguments))}}const qe=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},Ke=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ge=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ye=/^\d+\/\d+$/,Xe=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ze=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Je=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,et=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,tt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,nt=e=>Ye.test(e),rt=e=>!!e&&!Number.isNaN(Number(e)),at=e=>!!e&&Number.isInteger(Number(e)),lt=e=>e.endsWith("%")&&rt(e.slice(0,-1)),ot=e=>Xe.test(e),it=()=>!0,ut=e=>Ze.test(e)&&!Je.test(e),st=()=>!1,ct=e=>et.test(e),dt=e=>tt.test(e),ft=e=>!mt(e)&&!kt(e),pt=e=>Nt(e,Tt,st),mt=e=>Ke.test(e),ht=e=>Nt(e,Ot,ut),gt=e=>Nt(e,Rt,rt),vt=e=>Nt(e,zt,st),bt=e=>Nt(e,Lt,dt),yt=e=>Nt(e,jt,ct),kt=e=>Ge.test(e),wt=e=>Pt(e,Ot),xt=e=>Pt(e,Mt),St=e=>Pt(e,zt),Et=e=>Pt(e,Tt),Ct=e=>Pt(e,Lt),_t=e=>Pt(e,jt,!0),Nt=(e,t,n)=>{const r=Ke.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},Pt=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=Ge.exec(e);return!!r&&(r[1]?t(r[1]):n)},zt=e=>"position"===e||"percentage"===e,Lt=e=>"image"===e||"url"===e,Tt=e=>"length"===e||"size"===e||"bg-size"===e,Ot=e=>"length"===e,Rt=e=>"number"===e,Mt=e=>"family-name"===e,jt=e=>"shadow"===e,Dt=(Symbol.toStringTag,()=>{const e=qe("color"),t=qe("font"),n=qe("text"),r=qe("font-weight"),a=qe("tracking"),l=qe("leading"),o=qe("breakpoint"),i=qe("container"),u=qe("spacing"),s=qe("radius"),c=qe("shadow"),d=qe("inset-shadow"),f=qe("text-shadow"),p=qe("drop-shadow"),m=qe("blur"),h=qe("perspective"),g=qe("aspect"),v=qe("ease"),b=qe("animate"),y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",kt,mt],k=()=>[kt,mt,u],w=()=>[nt,"full","auto",...k()],x=()=>[at,"none","subgrid",kt,mt],S=()=>["auto",{span:["full",at,kt,mt]},at,kt,mt],E=()=>[at,"auto",kt,mt],C=()=>["auto","min","max","fr",kt,mt],_=()=>["auto",...k()],N=()=>[nt,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],P=()=>[e,kt,mt],z=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",St,vt,{position:[kt,mt]}],L=()=>["auto","cover","contain",Et,pt,{size:[kt,mt]}],T=()=>[lt,wt,ht],O=()=>["","none","full",s,kt,mt],R=()=>["",rt,wt,ht],M=()=>[rt,lt,St,vt],j=()=>["","none",m,kt,mt],D=()=>["none",rt,kt,mt],F=()=>["none",rt,kt,mt],I=()=>[rt,kt,mt],U=()=>[nt,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ot],breakpoint:[ot],color:[it],container:[ot],"drop-shadow":[ot],ease:["in","out","in-out"],font:[ft],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ot],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ot],shadow:[ot],spacing:["px",rt],text:[ot],"text-shadow":[ot],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",nt,mt,kt,g]}],container:["container"],columns:[{columns:[rt,mt,kt,i]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:w()}],"inset-x":[{"inset-x":w()}],"inset-y":[{"inset-y":w()}],start:[{start:w()}],end:[{end:w()}],top:[{top:w()}],right:[{right:w()}],bottom:[{bottom:w()}],left:[{left:w()}],visibility:["visible","invisible","collapse"],z:[{z:[at,"auto",kt,mt]}],basis:[{basis:[nt,"full","auto",i,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[rt,nt,"auto","initial","none",mt]}],grow:[{grow:["",rt,kt,mt]}],shrink:[{shrink:["",rt,kt,mt]}],order:[{order:[at,"first","last","none",kt,mt]}],"grid-cols":[{"grid-cols":x()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":E()}],"col-end":[{"col-end":E()}],"grid-rows":[{"grid-rows":x()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":E()}],"row-end":[{"row-end":E()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":C()}],"auto-rows":[{"auto-rows":C()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe","normal"]}],"justify-items":[{"justify-items":["start","end","center","stretch","center-safe","end-safe","normal"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"align-items":[{items:["start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"align-self":[{self:["auto","start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"place-items":[{"place-items":["start","end","center","stretch","center-safe","end-safe","baseline"]}],"place-self":[{"place-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:N()}],w:[{w:[i,"screen",...N()]}],"min-w":[{"min-w":[i,"screen","none",...N()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[o]},...N()]}],h:[{h:["screen","lh",...N()]}],"min-h":[{"min-h":["screen","lh","none",...N()]}],"max-h":[{"max-h":["screen","lh",...N()]}],"font-size":[{text:["base",n,wt,ht]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,kt,gt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",lt,mt]}],"font-family":[{font:[xt,mt,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,kt,mt]}],"line-clamp":[{"line-clamp":[rt,"none",kt,gt]}],leading:[{leading:[l,...k()]}],"list-image":[{"list-image":["none",kt,mt]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",kt,mt]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","wavy"]}],"text-decoration-thickness":[{decoration:[rt,"from-font","auto",kt,ht]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[rt,"auto",kt,mt]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",kt,mt]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",kt,mt]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:z()}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},at,kt,mt],radial:["",kt,mt],conic:[at,kt,mt]},Ct,bt]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:T()}],"gradient-via-pos":[{via:T()}],"gradient-to-pos":[{to:T()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:O()}],"rounded-s":[{"rounded-s":O()}],"rounded-e":[{"rounded-e":O()}],"rounded-t":[{"rounded-t":O()}],"rounded-r":[{"rounded-r":O()}],"rounded-b":[{"rounded-b":O()}],"rounded-l":[{"rounded-l":O()}],"rounded-ss":[{"rounded-ss":O()}],"rounded-se":[{"rounded-se":O()}],"rounded-ee":[{"rounded-ee":O()}],"rounded-es":[{"rounded-es":O()}],"rounded-tl":[{"rounded-tl":O()}],"rounded-tr":[{"rounded-tr":O()}],"rounded-br":[{"rounded-br":O()}],"rounded-bl":[{"rounded-bl":O()}],"border-w":[{border:R()}],"border-w-x":[{"border-x":R()}],"border-w-y":[{"border-y":R()}],"border-w-s":[{"border-s":R()}],"border-w-e":[{"border-e":R()}],"border-w-t":[{"border-t":R()}],"border-w-r":[{"border-r":R()}],"border-w-b":[{"border-b":R()}],"border-w-l":[{"border-l":R()}],"divide-x":[{"divide-x":R()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":R()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:["solid","dashed","dotted","double","hidden","none"]}],"divide-style":[{divide:["solid","dashed","dotted","double","hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:["solid","dashed","dotted","double","none","hidden"]}],"outline-offset":[{"outline-offset":[rt,kt,mt]}],"outline-w":[{outline:["",rt,wt,ht]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",c,_t,yt]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",d,_t,yt]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:R()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[rt,ht]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":R()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",f,_t,yt]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[rt,kt,mt]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[rt]}],"mask-image-linear-from-pos":[{"mask-linear-from":M()}],"mask-image-linear-to-pos":[{"mask-linear-to":M()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":M()}],"mask-image-t-to-pos":[{"mask-t-to":M()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":M()}],"mask-image-r-to-pos":[{"mask-r-to":M()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":M()}],"mask-image-b-to-pos":[{"mask-b-to":M()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":M()}],"mask-image-l-to-pos":[{"mask-l-to":M()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":M()}],"mask-image-x-to-pos":[{"mask-x-to":M()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":M()}],"mask-image-y-to-pos":[{"mask-y-to":M()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[kt,mt]}],"mask-image-radial-from-pos":[{"mask-radial-from":M()}],"mask-image-radial-to-pos":[{"mask-radial-to":M()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"]}],"mask-image-conic-pos":[{"mask-conic":[rt]}],"mask-image-conic-from-pos":[{"mask-conic-from":M()}],"mask-image-conic-to-pos":[{"mask-conic-to":M()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:z()}],"mask-repeat":[{mask:["no-repeat",{repeat:["","x","y","space","round"]}]}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",kt,mt]}],filter:[{filter:["","none",kt,mt]}],blur:[{blur:j()}],brightness:[{brightness:[rt,kt,mt]}],contrast:[{contrast:[rt,kt,mt]}],"drop-shadow":[{"drop-shadow":["","none",p,_t,yt]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",rt,kt,mt]}],"hue-rotate":[{"hue-rotate":[rt,kt,mt]}],invert:[{invert:["",rt,kt,mt]}],saturate:[{saturate:[rt,kt,mt]}],sepia:[{sepia:["",rt,kt,mt]}],"backdrop-filter":[{"backdrop-filter":["","none",kt,mt]}],"backdrop-blur":[{"backdrop-blur":j()}],"backdrop-brightness":[{"backdrop-brightness":[rt,kt,mt]}],"backdrop-contrast":[{"backdrop-contrast":[rt,kt,mt]}],"backdrop-grayscale":[{"backdrop-grayscale":["",rt,kt,mt]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[rt,kt,mt]}],"backdrop-invert":[{"backdrop-invert":["",rt,kt,mt]}],"backdrop-opacity":[{"backdrop-opacity":[rt,kt,mt]}],"backdrop-saturate":[{"backdrop-saturate":[rt,kt,mt]}],"backdrop-sepia":[{"backdrop-sepia":["",rt,kt,mt]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",kt,mt]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[rt,"initial",kt,mt]}],ease:[{ease:["linear","initial",v,kt,mt]}],delay:[{delay:[rt,kt,mt]}],animate:[{animate:["none",b,kt,mt]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,kt,mt]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:D()}],"rotate-x":[{"rotate-x":D()}],"rotate-y":[{"rotate-y":D()}],"rotate-z":[{"rotate-z":D()}],scale:[{scale:F()}],"scale-x":[{"scale-x":F()}],"scale-y":[{"scale-y":F()}],"scale-z":[{"scale-z":F()}],"scale-3d":["scale-3d"],skew:[{skew:I()}],"skew-x":[{"skew-x":I()}],"skew-y":[{"skew-y":I()}],transform:[{transform:[kt,mt,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:U()}],"translate-x":[{"translate-x":U()}],"translate-y":[{"translate-y":U()}],"translate-z":[{"translate-z":U()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",kt,mt]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",kt,mt]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[rt,wt,ht,gt]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),Ft=Qe(Dt);function It(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Ft(Le(t))}var Ut=n(579);const $t=e=>{let{className:t}=e;const n=[{icon:_e,label:"Tracked channels",active:!0,href:"/"},{icon:Ne,label:"Saved videos",active:!1,href:"/saved"},{icon:Ne,label:"Learn",active:!1,href:"/learn"}];return(0,Ut.jsxs)("div",{className:It("w-64 bg-dark-950 border-r border-dark-800 h-screen p-4",t),children:[(0,Ut.jsxs)("div",{className:"flex items-center gap-2 mb-8 px-2",children:[(0,Ut.jsx)("div",{className:"w-8 h-8 bg-brand-500 rounded-lg flex items-center justify-center",children:(0,Ut.jsx)(Pe,{className:"w-5 h-5 text-white fill-white"})}),(0,Ut.jsx)("span",{className:"text-xl font-bold text-white",children:"VELIO"}),(0,Ut.jsx)("span",{className:"text-xs text-dark-400 bg-dark-800 px-2 py-1 rounded",children:"Beta"})]}),(0,Ut.jsx)("nav",{className:"space-y-1",children:n.map(e=>{const t=e.icon;return(0,Ut.jsxs)("a",{href:e.href,className:It("flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",e.active?"bg-brand-500/10 text-brand-400 border border-brand-500/20":"text-dark-400 hover:text-white hover:bg-dark-800"),children:[(0,Ut.jsx)(t,{className:"w-5 h-5"}),e.label]},e.label)})})]})},At=e=>{let t;const n=new Set,r=(e,r)=>{const a="function"===typeof e?e(t):e;if(!Object.is(a,t)){const e=t;t=(null!=r?r:"object"!==typeof a||null===a)?a:Object.assign({},t,a),n.forEach(n=>n(t,e))}},a=()=>t,l={setState:r,getState:a,getInitialState:()=>o,subscribe:e=>(n.add(e),()=>n.delete(e))},o=t=e(r,a,l);return l},Bt=e=>e;const Vt=e=>{const t=(e=>e?At(e):At)(e),n=e=>function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bt;const n=a.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return a.useDebugValue(n),n}(t,e);return Object.assign(n,t),n},Wt=(Ht=e=>({channels:[],channelLists:[{id:"test",name:"test",channels:[],created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}],selectedList:"test",loading:!1,error:null,setChannels:t=>e({channels:t}),addChannel:t=>e(e=>({channels:[...e.channels,t]})),removeChannel:t=>e(e=>({channels:e.channels.filter(e=>e.channel_id!==t)})),updateChannel:t=>e(e=>({channels:e.channels.map(e=>e.channel_id===t.channel_id?t:e)})),setLoading:t=>e({loading:t}),setError:t=>e({error:t}),setChannelLists:t=>e({channelLists:t}),addChannelList:t=>e(e=>({channelLists:[...e.channelLists,t]})),removeChannelList:t=>e(e=>({channelLists:e.channelLists.filter(e=>e.id!==t)})),setSelectedList:t=>e({selectedList:t})}))?Vt(Ht):Vt;var Ht;const Qt=Ce("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),qt=e=>{let{onCreate:t,className:n}=e;const[r,l]=(0,a.useState)(!1),[o,i]=(0,a.useState)(!1),[u,s]=(0,a.useState)(""),c=()=>{u.trim()&&(t(u.trim()),s(""),i(!1))},d=e=>{"Enter"===e.key?c():"Escape"===e.key&&(s(""),i(!1))};return o?(0,Ut.jsx)("div",{className:It("px-4 py-3",n),children:(0,Ut.jsx)("input",{type:"text",value:u,onChange:e=>s(e.target.value),onBlur:c,onKeyDown:d,placeholder:"Enter list name...",className:"w-full bg-dark-800 border border-dark-600 text-white placeholder-dark-400 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent",autoFocus:!0})}):(0,Ut.jsxs)("button",{onClick:()=>i(!0),onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),className:It("w-full flex items-center gap-3 px-4 py-3 rounded-lg border-2 border-dashed transition-all duration-200",r?"border-brand-500 bg-brand-500/5 text-brand-400":"border-dark-600 text-dark-400 hover:text-dark-300",n),children:[(0,Ut.jsx)(Qt,{className:"w-5 h-5"}),(0,Ut.jsx)("span",{className:"text-sm font-medium",children:"Create new channel list"})]})},Kt=Ce("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),Gt=Ce("Pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]),Yt=Ce("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),Xt=Ce("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),Zt=Ce("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Jt=e=>{let{list:t,isSelected:n,onClick:r,onRename:l,onDuplicate:o,onDelete:i}=e;const[u,s]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[f,p]=(0,a.useState)(t.name),m=()=>{l&&f.trim()!==t.name&&l(t.id,f.trim()),d(!1)};return(0,Ut.jsxs)("div",{className:It("group relative flex items-center justify-between px-4 py-3 rounded-lg cursor-pointer transition-all duration-200",n?"bg-dark-800 border border-dark-600":"hover:bg-dark-900"),onClick:r,children:[(0,Ut.jsx)("div",{className:"flex-1 min-w-0",children:c?(0,Ut.jsx)("input",{type:"text",value:f,onChange:e=>p(e.target.value),onBlur:m,onKeyDown:e=>{"Enter"===e.key?m():"Escape"===e.key&&(p(t.name),d(!1))},className:"bg-transparent text-white border-none outline-none w-full",autoFocus:!0}):(0,Ut.jsx)("span",{className:"text-white text-sm font-medium truncate",children:t.name})}),(0,Ut.jsxs)("div",{className:"relative",children:[(0,Ut.jsx)("button",{onClick:e=>{e.stopPropagation(),s(!u)},className:"opacity-0 group-hover:opacity-100 p-1 hover:bg-dark-700 rounded transition-all duration-200",children:(0,Ut.jsx)(Kt,{className:"w-4 h-4 text-dark-400"})}),u&&(0,Ut.jsxs)("div",{className:"absolute right-0 top-8 w-40 bg-dark-800 border border-dark-600 rounded-lg shadow-lg z-10 py-1",children:[(0,Ut.jsxs)("button",{onClick:e=>{e.stopPropagation(),s(!1)},className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-dark-300 hover:text-white hover:bg-dark-700 transition-colors",children:[(0,Ut.jsx)(Gt,{className:"w-4 h-4"}),"Pin"]}),(0,Ut.jsxs)("button",{onClick:e=>{e.stopPropagation(),s(!1),d(!0)},className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-dark-300 hover:text-white hover:bg-dark-700 transition-colors",children:[(0,Ut.jsx)(Yt,{className:"w-4 h-4"}),"Rename"]}),(0,Ut.jsxs)("button",{onClick:e=>{e.stopPropagation(),s(!1),null===o||void 0===o||o(t.id)},className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-dark-300 hover:text-white hover:bg-dark-700 transition-colors",children:[(0,Ut.jsx)(Xt,{className:"w-4 h-4"}),"Duplicate"]}),(0,Ut.jsxs)("button",{onClick:e=>{e.stopPropagation(),s(!1),null===i||void 0===i||i(t.id)},className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-dark-700 transition-colors",children:[(0,Ut.jsx)(Zt,{className:"w-4 h-4"}),"Delete"]})]})]})]})},en=()=>{const{channelLists:e,selectedList:t,addChannelList:n,removeChannelList:r,setSelectedList:a}=Wt(),l=(e,t)=>{console.log("Rename list:",e,t)},o=t=>{const r=e.find(e=>e.id===t);if(r){const e=be(be({},r),{},{id:"list-".concat(Date.now()),name:"".concat(r.name," (Copy)"),created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()});n(e)}},i=n=>{if(r(n),t===n&&e.length>1){var l;const t=e.filter(e=>e.id!==n);a((null===(l=t[0])||void 0===l?void 0:l.id)||null)}};return(0,Ut.jsxs)("div",{className:"flex-1 bg-dark-950 p-6",children:[(0,Ut.jsx)("header",{className:"mb-8",children:(0,Ut.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Tracked channels"})}),(0,Ut.jsxs)("div",{className:"grid grid-cols-2 gap-6 max-w-2xl",children:[(0,Ut.jsx)("div",{className:"space-y-4",children:(0,Ut.jsx)(qt,{onCreate:e=>{const t={id:"list-".concat(Date.now()),name:e,channels:[],created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()};n(t)}})}),(0,Ut.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,Ut.jsx)(Jt,{list:e,isSelected:t===e.id,onClick:()=>a(e.id),onRename:l,onDuplicate:o,onDelete:i},e.id))})]})]})},tn=()=>(0,Ut.jsx)(de,{children:(0,Ut.jsxs)("div",{className:"flex h-screen bg-dark-950 overflow-hidden",children:[(0,Ut.jsx)($t,{}),(0,Ut.jsx)("main",{className:"flex-1",children:(0,Ut.jsxs)(ue,{children:[(0,Ut.jsx)(oe,{path:"/",element:(0,Ut.jsx)(en,{})}),(0,Ut.jsx)(oe,{path:"/saved",element:(0,Ut.jsx)("div",{className:"p-6 text-white h-full flex items-center justify-center",children:(0,Ut.jsxs)("div",{className:"text-center",children:[(0,Ut.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Saved Videos"}),(0,Ut.jsx)("p",{className:"text-dark-400",children:"Coming Soon"})]})})}),(0,Ut.jsx)(oe,{path:"/learn",element:(0,Ut.jsx)("div",{className:"p-6 text-white h-full flex items-center justify-center",children:(0,Ut.jsxs)("div",{className:"text-center",children:[(0,Ut.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Learn"}),(0,Ut.jsx)("p",{className:"text-dark-400",children:"Coming Soon"})]})})})]})})]})});o.createRoot(document.getElementById("root")).render((0,Ut.jsx)(a.StrictMode,{children:(0,Ut.jsx)(tn,{})}))})();
//# sourceMappingURL=main.4b1535d0.js.map