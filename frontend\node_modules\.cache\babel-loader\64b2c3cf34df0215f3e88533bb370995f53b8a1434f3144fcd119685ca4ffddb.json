{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Lectern = createLucideIcon(\"Lectern\", [[\"path\", {\n  d: \"M16 12h3a2 2 0 0 0 1.902-1.38l1.056-3.333A1 1 0 0 0 21 6H3a1 1 0 0 0-.958 1.287l1.056 3.334A2 2 0 0 0 5 12h3\",\n  key: \"13jjxg\"\n}], [\"path\", {\n  d: \"M18 6V3a1 1 0 0 0-1-1h-3\",\n  key: \"1550fe\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"12\",\n  x: \"8\",\n  y: \"10\",\n  rx: \"1\",\n  key: \"qmu8b6\"\n}]]);\nexport { Lectern as default };", "map": {"version": 3, "names": ["Lectern", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\lectern.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Lectern\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTJoM2EyIDIgMCAwIDAgMS45MDItMS4zOGwxLjA1Ni0zLjMzM0ExIDEgMCAwIDAgMjEgNkgzYTEgMSAwIDAgMC0uOTU4IDEuMjg3bDEuMDU2IDMuMzM0QTIgMiAwIDAgMCA1IDEyaDMiIC8+CiAgPHBhdGggZD0iTTE4IDZWM2ExIDEgMCAwIDAtMS0xaC0zIiAvPgogIDxyZWN0IHdpZHRoPSI4IiBoZWlnaHQ9IjEyIiB4PSI4IiB5PSIxMCIgcng9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/lectern\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lectern = createLucideIcon('Lectern', [\n  [\n    'path',\n    {\n      d: 'M16 12h3a2 2 0 0 0 1.902-1.38l1.056-3.333A1 1 0 0 0 21 6H3a1 1 0 0 0-.958 1.287l1.056 3.334A2 2 0 0 0 5 12h3',\n      key: '13jjxg',\n    },\n  ],\n  ['path', { d: 'M18 6V3a1 1 0 0 0-1-1h-3', key: '1550fe' }],\n  ['rect', { width: '8', height: '12', x: '8', y: '10', rx: '1', key: 'qmu8b6' }],\n]);\n\nexport default Lectern;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}