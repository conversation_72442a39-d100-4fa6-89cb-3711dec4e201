{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Soup = createLucideIcon(\"Soup\", [[\"path\", {\n  d: \"M12 21a9 9 0 0 0 9-9H3a9 9 0 0 0 9 9Z\",\n  key: \"4rw317\"\n}], [\"path\", {\n  d: \"M7 21h10\",\n  key: \"1b0cd5\"\n}], [\"path\", {\n  d: \"M19.5 12 22 6\",\n  key: \"shfsr5\"\n}], [\"path\", {\n  d: \"M16.25 3c.*********.75 1.36-.06.83-.93 1.2-1 2.02-.05.78.34 1.24.73 1.62\",\n  key: \"rpc6vp\"\n}], [\"path\", {\n  d: \"M11.25 3c.*********.74 1.36-.05.83-.93 1.2-.98 2.02-.06.78.33 1.24.72 1.62\",\n  key: \"1lf63m\"\n}], [\"path\", {\n  d: \"M6.25 3c.*********.75 1.36-.06.83-.93 1.2-1 2.02-.05.78.34 1.24.74 1.62\",\n  key: \"97tijn\"\n}]]);\nexport { Soup as default };", "map": {"version": 3, "names": ["Soup", "createLucideIcon", "d", "key"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\soup.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Soup\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/soup\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Soup = createLucideIcon('Soup', [\n  ['path', { d: 'M12 21a9 9 0 0 0 9-9H3a9 9 0 0 0 9 9Z', key: '4rw317' }],\n  ['path', { d: 'M7 21h10', key: '1b0cd5' }],\n  ['path', { d: 'M19.5 12 22 6', key: 'shfsr5' }],\n  [\n    'path',\n    {\n      d: 'M16.25 3c.*********.75 1.36-.06.83-.93 1.2-1 2.02-.05.78.34 1.24.73 1.62',\n      key: 'rpc6vp',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M11.25 3c.*********.74 1.36-.05.83-.93 1.2-.98 2.02-.06.78.33 1.24.72 1.62',\n      key: '1lf63m',\n    },\n  ],\n  [\n    'path',\n    { d: 'M6.25 3c.*********.75 1.36-.06.83-.93 1.2-1 2.02-.05.78.34 1.24.74 1.62', key: '97tijn' },\n  ],\n]);\n\nexport default Soup;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EAAED,CAAA,EAAG,yEAA2E;EAAAC,GAAA,EAAK;AAAS,EAChG,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}