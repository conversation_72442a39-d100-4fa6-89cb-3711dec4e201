{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Replace = createLucideIcon(\"Replace\", [[\"path\", {\n  d: \"M14 4a2 2 0 0 1 2-2\",\n  key: \"1w2hp7\"\n}], [\"path\", {\n  d: \"M16 10a2 2 0 0 1-2-2\",\n  key: \"shjach\"\n}], [\"path\", {\n  d: \"M20 2a2 2 0 0 1 2 2\",\n  key: \"188mtx\"\n}], [\"path\", {\n  d: \"M22 8a2 2 0 0 1-2 2\",\n  key: \"ddf4tu\"\n}], [\"path\", {\n  d: \"m3 7 3 3 3-3\",\n  key: \"x25e72\"\n}], [\"path\", {\n  d: \"M6 10V5a3 3 0 0 1 3-3h1\",\n  key: \"3y3t5z\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"14\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"4rksxw\"\n}]]);\nexport { Replace as default };", "map": {"version": 3, "names": ["Replace", "createLucideIcon", "d", "key", "x", "y", "width", "height", "rx"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\replace.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Replace\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgNGEyIDIgMCAwIDEgMi0yIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMGEyIDIgMCAwIDEtMi0yIiAvPgogIDxwYXRoIGQ9Ik0yMCAyYTIgMiAwIDAgMSAyIDIiIC8+CiAgPHBhdGggZD0iTTIyIDhhMiAyIDAgMCAxLTIgMiIgLz4KICA8cGF0aCBkPSJtMyA3IDMgMyAzLTMiIC8+CiAgPHBhdGggZD0iTTYgMTBWNWEzIDMgMCAwIDEgMy0zaDEiIC8+CiAgPHJlY3QgeD0iMiIgeT0iMTQiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/replace\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Replace = createLucideIcon('Replace', [\n  ['path', { d: 'M14 4a2 2 0 0 1 2-2', key: '1w2hp7' }],\n  ['path', { d: 'M16 10a2 2 0 0 1-2-2', key: 'shjach' }],\n  ['path', { d: 'M20 2a2 2 0 0 1 2 2', key: '188mtx' }],\n  ['path', { d: 'M22 8a2 2 0 0 1-2 2', key: 'ddf4tu' }],\n  ['path', { d: 'm3 7 3 3 3-3', key: 'x25e72' }],\n  ['path', { d: 'M6 10V5a3 3 0 0 1 3-3h1', key: '3y3t5z' }],\n  ['rect', { x: '2', y: '14', width: '8', height: '8', rx: '2', key: '4rksxw' }],\n]);\n\nexport default Replace;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAMC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}