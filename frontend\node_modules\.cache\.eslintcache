[{"C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\index.tsx": "1", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\App.tsx": "2", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\TrackedChannels.tsx": "3", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\Sidebar.tsx": "4", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\CreateChannelListButton.tsx": "5", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\ChannelListItem.tsx": "6", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\store\\channelStore.ts": "7", "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\utils\\index.ts": "8"}, {"size": 274, "mtime": 1751054797351, "results": "9", "hashOfConfig": "10"}, {"size": 1320, "mtime": 1751054908423, "results": "11", "hashOfConfig": "10"}, {"size": 2449, "mtime": 1751058771652, "results": "12", "hashOfConfig": "10"}, {"size": 1832, "mtime": 1751058784797, "results": "13", "hashOfConfig": "10"}, {"size": 2004, "mtime": 1751058797988, "results": "14", "hashOfConfig": "10"}, {"size": 4212, "mtime": 1751058810846, "results": "15", "hashOfConfig": "10"}, {"size": 1898, "mtime": 1751058836573, "results": "16", "hashOfConfig": "10"}, {"size": 1155, "mtime": 1751051364813, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hgmcsp", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\index.tsx", [], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\App.tsx", [], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\TrackedChannels.tsx", ["42", "43", "44"], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\Sidebar.tsx", [], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\CreateChannelListButton.tsx", [], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\components\\ChannelListItem.tsx", [], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\store\\channelStore.ts", [], [], "C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\src\\utils\\index.ts", [], [], {"ruleId": "45", "severity": 2, "message": "46", "line": 1, "column": 17, "nodeType": "47", "messageId": "48", "endLine": 1, "endColumn": 25}, {"ruleId": "45", "severity": 2, "message": "49", "line": 1, "column": 27, "nodeType": "47", "messageId": "48", "endLine": 1, "endColumn": 36}, {"ruleId": "50", "severity": 1, "message": "51", "line": 29, "column": 5, "nodeType": "52", "messageId": "53", "endLine": 29, "endColumn": 16, "suggestions": "54"}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["55"], {"messageId": "56", "data": "57", "fix": "58", "desc": "59"}, "removeConsole", {"propertyName": "60"}, {"range": "61", "text": "62"}, "Remove the console.log().", "log", [824, 865], ""]