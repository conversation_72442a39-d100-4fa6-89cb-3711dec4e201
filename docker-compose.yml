services:
  postgres:
    image: postgres:16
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-youtube_stats}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - youtube-stats-network
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL:-********************************************/youtube_stats}
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - CORS_ORIGINS=["http://localhost:3000", "http://frontend"]
      - DEBUG=${DEBUG:-false}
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - youtube-stats-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   ports:
  #     - "3000:80"
  #   environment:
  #     - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
  #   depends_on:
  #     - backend
  #   networks:
  #     - youtube-stats-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost/"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

networks:
  youtube-stats-network:
    driver: bridge

volumes:
  postgres_data: