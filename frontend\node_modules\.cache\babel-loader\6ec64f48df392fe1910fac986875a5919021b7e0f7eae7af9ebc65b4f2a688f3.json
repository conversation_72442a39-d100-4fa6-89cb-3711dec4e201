{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PaintRoller = createLucideIcon(\"PaintRoller\", [[\"rect\", {\n  width: \"16\",\n  height: \"6\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"jcyz7m\"\n}], [\"path\", {\n  d: \"M10 16v-2a2 2 0 0 1 2-2h8a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2\",\n  key: \"1b9h7c\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"6\",\n  x: \"8\",\n  y: \"16\",\n  rx: \"1\",\n  key: \"d6e7yl\"\n}]]);\nexport { PaintRoller as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\paint-roller.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PaintRoller\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iNiIgeD0iMiIgeT0iMiIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTEwIDE2di0yYTIgMiAwIDAgMSAyLTJoOGEyIDIgMCAwIDAgMi0yVjdhMiAyIDAgMCAwLTItMmgtMiIgLz4KICA8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSI2IiB4PSI4IiB5PSIxNiIgcng9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/paint-roller\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PaintRoller = createLucideIcon('PaintRoller', [\n  ['rect', { width: '16', height: '6', x: '2', y: '2', rx: '2', key: 'jcyz7m' }],\n  ['path', { d: 'M10 16v-2a2 2 0 0 1 2-2h8a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2', key: '1b9h7c' }],\n  ['rect', { width: '4', height: '6', x: '8', y: '16', rx: '1', key: 'd6e7yl' }],\n]);\n\nexport default PaintRoller;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAEC,CAAA,EAAG,4DAA8D;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3F,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}