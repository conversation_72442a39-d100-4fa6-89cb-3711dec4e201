{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Satellite = createLucideIcon(\"Satellite\", [[\"path\", {\n  d: \"M13 7 9 3 5 7l4 4\",\n  key: \"vyckw6\"\n}], [\"path\", {\n  d: \"m17 11 4 4-4 4-4-4\",\n  key: \"rchckc\"\n}], [\"path\", {\n  d: \"m8 12 4 4 6-6-4-4Z\",\n  key: \"1sshf7\"\n}], [\"path\", {\n  d: \"m16 8 3-3\",\n  key: \"x428zp\"\n}], [\"path\", {\n  d: \"M9 21a6 6 0 0 0-6-6\",\n  key: \"1iajcf\"\n}]]);\nexport { Satellite as default };", "map": {"version": 3, "names": ["Satellite", "createLucideIcon", "d", "key"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\satellite.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Satellite\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgNyA5IDMgNSA3bDQgNCIgLz4KICA8cGF0aCBkPSJtMTcgMTEgNCA0LTQgNC00LTQiIC8+CiAgPHBhdGggZD0ibTggMTIgNCA0IDYtNi00LTRaIiAvPgogIDxwYXRoIGQ9Im0xNiA4IDMtMyIgLz4KICA8cGF0aCBkPSJNOSAyMWE2IDYgMCAwIDAtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/satellite\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Satellite = createLucideIcon('Satellite', [\n  ['path', { d: 'M13 7 9 3 5 7l4 4', key: 'vyckw6' }],\n  ['path', { d: 'm17 11 4 4-4 4-4-4', key: 'rchckc' }],\n  ['path', { d: 'm8 12 4 4 6-6-4-4Z', key: '1sshf7' }],\n  ['path', { d: 'm16 8 3-3', key: 'x428zp' }],\n  ['path', { d: 'M9 21a6 6 0 0 0-6-6', key: '1iajcf' }],\n]);\n\nexport default Satellite;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}