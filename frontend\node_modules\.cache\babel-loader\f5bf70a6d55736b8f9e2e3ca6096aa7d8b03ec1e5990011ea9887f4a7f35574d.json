{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst MonitorCog = createLucideIcon(\"MonitorCog\", [[\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"m15.2 4.9-.9-.4\",\n  key: \"12wd2u\"\n}], [\"path\", {\n  d: \"m15.2 7.1-.9.4\",\n  key: \"1r2vl7\"\n}], [\"path\", {\n  d: \"m16.9 3.2-.4-.9\",\n  key: \"3zbo91\"\n}], [\"path\", {\n  d: \"m16.9 8.8-.4.9\",\n  key: \"1qr2dn\"\n}], [\"path\", {\n  d: \"m19.5 2.3-.4.9\",\n  key: \"1rjrkq\"\n}], [\"path\", {\n  d: \"m19.5 9.7-.4-.9\",\n  key: \"heryx5\"\n}], [\"path\", {\n  d: \"m21.7 4.5-.9.4\",\n  key: \"17fqt1\"\n}], [\"path\", {\n  d: \"m21.7 7.5-.9-.4\",\n  key: \"14zyni\"\n}], [\"path\", {\n  d: \"M22 13v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7\",\n  key: \"1tnzv8\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}]]);\nexport { MonitorCog as default };", "map": {"version": 3, "names": ["MonitorCog", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\monitor-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MonitorCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTd2NCIgLz4KICA8cGF0aCBkPSJtMTUuMiA0LjktLjktLjQiIC8+CiAgPHBhdGggZD0ibTE1LjIgNy4xLS45LjQiIC8+CiAgPHBhdGggZD0ibTE2LjkgMy4yLS40LS45IiAvPgogIDxwYXRoIGQ9Im0xNi45IDguOC0uNC45IiAvPgogIDxwYXRoIGQ9Im0xOS41IDIuMy0uNC45IiAvPgogIDxwYXRoIGQ9Im0xOS41IDkuNy0uNC0uOSIgLz4KICA8cGF0aCBkPSJtMjEuNyA0LjUtLjkuNCIgLz4KICA8cGF0aCBkPSJtMjEuNyA3LjUtLjktLjQiIC8+CiAgPHBhdGggZD0iTTIyIDEzdjJhMiAyIDAgMCAxLTIgMkg0YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDciIC8+CiAgPHBhdGggZD0iTTggMjFoOCIgLz4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjYiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/monitor-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MonitorCog = createLucideIcon('MonitorCog', [\n  ['path', { d: 'M12 17v4', key: '1riwvh' }],\n  ['path', { d: 'm15.2 4.9-.9-.4', key: '12wd2u' }],\n  ['path', { d: 'm15.2 7.1-.9.4', key: '1r2vl7' }],\n  ['path', { d: 'm16.9 3.2-.4-.9', key: '3zbo91' }],\n  ['path', { d: 'm16.9 8.8-.4.9', key: '1qr2dn' }],\n  ['path', { d: 'm19.5 2.3-.4.9', key: '1rjrkq' }],\n  ['path', { d: 'm19.5 9.7-.4-.9', key: 'heryx5' }],\n  ['path', { d: 'm21.7 4.5-.9.4', key: '17fqt1' }],\n  ['path', { d: 'm21.7 7.5-.9-.4', key: '14zyni' }],\n  ['path', { d: 'M22 13v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7', key: '1tnzv8' }],\n  ['path', { d: 'M8 21h8', key: '1ev6f3' }],\n  ['circle', { cx: '18', cy: '6', r: '3', key: '1h7g24' }],\n]);\n\nexport default MonitorCog;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}