{"ast": null, "code": "/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SkipBack = createLucideIcon(\"SkipBack\", [[\"polygon\", {\n  points: \"19 20 9 12 19 4 19 20\",\n  key: \"o2sva\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"5\",\n  y1: \"19\",\n  y2: \"5\",\n  key: \"1ocqjk\"\n}]]);\nexport { SkipBack as default };", "map": {"version": 3, "names": ["SkipBack", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\MY_PROJECTS\\youtube-stats-tracker-modernized\\frontend\\node_modules\\lucide-react\\src\\icons\\skip-back.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SkipBack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjE5IDIwIDkgMTIgMTkgNCAxOSAyMCIgLz4KICA8bGluZSB4MT0iNSIgeDI9IjUiIHkxPSIxOSIgeTI9IjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/skip-back\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SkipBack = createLucideIcon('SkipBack', [\n  ['polygon', { points: '19 20 9 12 19 4 19 20', key: 'o2sva' }],\n  ['line', { x1: '5', x2: '5', y1: '19', y2: '5', key: '1ocqjk' }],\n]);\n\nexport default SkipBack;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,SAAW;EAAEC,MAAA,EAAQ,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAS,GAC7D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}